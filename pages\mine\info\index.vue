<template>
  <view class="container">
    <uni-list>
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'person-filled'}" title="昵称" :rightText="user.nickName" />
	   <uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled'}" title="性别" :rightText="user.sex?'女':'男'" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'phone-filled'}" title="手机号码" :rightText="user.mobile" />
	  <uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled'}" title="部门" :rightText="user.departName" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'auth-filled'}" title="职务" :rightText="user.postName" />
      <uni-list-item showExtraIcon="true" :extraIcon="{type: 'staff-filled'}" title="角色" :rightText="user.userType" />
    </uni-list>
  </view>
</template>

<script>
  import { getUserInfo } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {},
      }
    },
    onLoad() {
      this.getUser()
    },
    methods: {
      getUser() {
        getUserInfo().then(response => {
          this.user = response;
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }
</style>
