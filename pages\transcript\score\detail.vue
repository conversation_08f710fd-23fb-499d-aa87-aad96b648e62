<template>
	<view class="page">
		<view class="header">
			<image class="header-icon" src="/static/images/trophy.png"></image>
			<text class="header-title"> 成绩明细</text>
			<text class="header-date">更新时间: {{day}}</text>
		</view>
		
		<!-- 头部区域 -->
		<view class="header">
			<div style="font-size: 64px;font-weight: bold;">“</div>
			<rich-text style="font-family: fangsong;text-indent: 24px; margin-bottom: 5%;" :nodes="scoreText[0]"></rich-text>
		</view>
		<!-- 管理学科目 -->
		<view class="ranking" v-if="GZList.length>0">
			<view class="ranking-list">
				<view class="ranking-list-item">
					<view class="ranking-list-number courseMy title-style">
						<text>科目</text>
					</view>
					<text class="ranking-list-number title-style">分数</text>
					<view class="ranking-list-nickname title-style">
						<text>排名</text>
					</view>
					<view class="ranking-list-nickname title-style">
						<text>升降</text>
					</view>
				</view>
			</view>
			<view class="ranking-list">
				<view class="ranking-list-item" v-for="(item, key) in GZList" :key="key">
					<view class="ranking-list-number courseMy">
						<text>{{item[0]}}</text>
					</view>
					<text class="ranking-list-score">{{item[1]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;">分</span></text>
					<view class="ranking-list-nickname">
						<text style="font-weight: 600;color: #717171;"><span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">第</span>{{item[2]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">名</span></text>
					</view>
					<view class="ranking-list-nickname" style="position: relative;height: 100%;font-weight: 400;">

						<text  v-show="item[3]>0" style="color: #E06C75">{{Math.abs(item[3])}}
							<uni-icons type="arrow-up" size="20" color="#E06C75" class="ab-tr"></uni-icons>
							
						</text>
						<text  v-show="item[3]<0" style="color: #77C2FA;">{{Math.abs(item[3])}}
						<uni-icons type="arrow-down" size="20" color="#77C2FA" class="ab-tr"></uni-icons>
						</text>
					</view>
				</view>
			</view>
			
		</view>
	
		<!-- 政英科目 -->
		<view class="ranking" v-if="ZYList.length>0">
			<view class="ranking-list">
				<view class="ranking-list-item">
					<view class="ranking-list-number courseMy title-style">
						<text>科目</text>
					</view>
					<text class="ranking-list-number title-style">分数</text>
					<view class="ranking-list-nickname title-style">
						<text>排名</text>
					</view>
					<view class="ranking-list-nickname title-style">
						<text>升降</text>
					</view>
				</view>
			</view>
			<view class="ranking-list">
				<view class="ranking-list-item" v-for="(item, key) in ZYList" :key="key">
					<view class="ranking-list-number courseMy">
						<text>{{item[0]}}</text>
					</view>
					<text class="ranking-list-score">{{item[1]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;">分</span></text>
					<view class="ranking-list-nickname">
						<text style="font-weight: 600;color: #717171;"><span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">第</span>{{item[2]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">名</span></text>
					</view>
					<view class="ranking-list-nickname" style="position: relative;height: 100%;font-weight: 400;">

						<text  v-show="item[3]>0" style="color: #E06C75">{{Math.abs(item[3])}}
							<uni-icons type="arrow-up" size="20" color="#E06C75" class="ab-tr"></uni-icons>
							
						</text>
						<text  v-show="item[3]<0" style="color: #77C2FA;">{{Math.abs(item[3])}}
						<uni-icons type="arrow-down" size="20" color="#77C2FA" class="ab-tr"></uni-icons>
						</text>
					</view>
				</view>
			</view>
			
		</view>
		
		<!-- 政数英科目 -->
		<view class="ranking" v-if="ZSYList.length>0">
			<view class="ranking-list">
				<view class="ranking-list-item">
					<view class="ranking-list-number courseMy title-style">
						<text>科目</text>
					</view>
					<text class="ranking-list-number title-style">分数</text>
					<view class="ranking-list-nickname title-style">
						<text>排名</text>
					</view>
					<view class="ranking-list-nickname title-style">
						<text>升降</text>
					</view>
				</view>
			</view>
			<view class="ranking-list">
				<view class="ranking-list-item" v-for="(item, key) in ZSYList" :key="key">
					<view class="ranking-list-number courseMy">
						<text>{{item[0]}}</text>
					</view>
					<text class="ranking-list-score">{{item[1]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;">分</span></text>
					<view class="ranking-list-nickname">
						<text style="font-weight: 600;color: #717171;"><span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">第</span>{{item[2]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">名</span></text>
					</view>
					<view class="ranking-list-nickname" style="position: relative;height: 100%;font-weight: 400;">
		
						<text  v-show="item[3]>0" style="color: #E06C75">{{Math.abs(item[3])}}
							<uni-icons type="arrow-up" size="20" color="#E06C75" class="ab-tr"></uni-icons>
							
						</text>
						<text  v-show="item[3]<0" style="color: #77C2FA;">{{Math.abs(item[3])}}
						<uni-icons type="arrow-down" size="20" color="#77C2FA" class="ab-tr"></uni-icons>
						</text>
					</view>
				</view>
			</view>
			
		</view>


		<!-- 经济学科目 -->
		<view class="ranking" v-if="JJList.length>0">
			<view class="ranking-list">
				<view class="ranking-list-item">
					<view class="ranking-list-number courseMy title-style">
						<text>科目</text>
					</view>
					<text class="ranking-list-number title-style">分数</text>
					<view class="ranking-list-nickname title-style">
						<text>排名</text>
					</view>
					<view class="ranking-list-nickname title-style">
						<text>升降</text>
					</view>
				</view>
			</view>
			<view class="ranking-list">
				<view class="ranking-list-item" v-for="(item, key) in JJList" :key="key">
					<view class="ranking-list-number courseMy">
						<text>{{item[0]}}</text>
					</view>
					<text class="ranking-list-score">{{item[1]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;">分</span></text>
					<view class="ranking-list-nickname">
						<text style="font-weight: 600;color: #717171;"><span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">第</span>{{item[2]}}<span
							style="font-size: 8px;font-weight: bold;padding-left: 2px;padding-right: 2px;">名</span></text>
					</view>
					<view class="ranking-list-nickname" style="position: relative;height: 100%;font-weight: 400;">

						<text  v-show="item[3]>0" style="color: #E06C75">{{Math.abs(item[3])}}
							<uni-icons type="arrow-up" size="20" color="#E06C75" class="ab-tr"></uni-icons>
							
						</text>
						<text  v-show="item[3]<0" style="color: #77C2FA;">{{Math.abs(item[3])}}
						<uni-icons type="arrow-down" size="20" color="#77C2FA" class="ab-tr"></uni-icons>
						</text>
					</view>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import storage from '../../../utils/storage';
	import request from '@/utils/request'
	import constant from '@/utils/constant'
	import user from '../../../store/modules/user';
	import upload from '../../../utils/upload';
	import download from '../../../utils/download';
	import chooseFile from '../../../utils/chooseFile';
	export default {
		data() {
			return {
				GZList: [],
				ZYList:[],
				ZSYList:[],
				JJList:[],
				scoreText:[],  //成绩激励语录
				day: '',
				id: 0, // 用户 ID
				scoreDetails: {}, // 成绩细则数据
				fileUrl: '', // 文件 URL
				formatScores: [], //成绩细则格式化数据
				pattern: {
					color: '#7A7E83',
					backgroundColor: '#fff',
					selectedColor: '#FF5733',
					buttonColor: '#FFE4B5',
					iconColor: '#fff'
				},
				isTeacher: true,
				buttons: [{
						text: '上传图片',
						active: false
					},
					{
						text: '上传PDF',
						active: false
					},
				],
			}
		},
		mounted() {
			this.isTeacher = storage.get(constant.roles).includes('管理员')
			this.day = this.formatDateToChinese(new Date())
		},
		onLoad(options) {
			this.id = options.id; // 从路由参数中获取用户 ID
			this.fetchScoreDetails(); // 获取成绩细则数据
			//this.fetchFileInfo(); // 获取文件信息

		},
		methods: {
			formatDateToChinese(dateInput) {
				const date = new Date(dateInput);

				// 校验日期有效性
				if (isNaN(date.getTime())) {
					throw new Error('Invalid date input');
				}

				// 获取日期组件
				const year = date.getFullYear();
				const month = date.getMonth() + 1; // 月份从0开始需+1
				const day = date.getDate();

				// 拼接中文格式
				return `${year}年${month}月${day}日`;
			},
			// 请求后端 API 获取成绩细则数据
			async fetchScoreDetails() {
				try {
					uni.showLoading({
						title: '加载中'
					})
					const response = await request({
						url: '/ApiCaScore/GetById', // 替换为你的成绩细则 API 地址
						method: 'POST',
						data: {
							id: this.id,
						},
					});

					if (response && response !== null) {
						if(response.totalUpOrDown>0){
							this.scoreText=[`恭喜在<span style="color: #E06C75;font-weight:bold;font-size:18px;">【${response.testPaperCode}】</span>中，获得<span style="color: #E06C75;font-weight:bold;font-size:18px;">${response.totalScore}</span>分，排名前进了一大截！看来学霸附体了，继续保持这股劲头哦！`]
						}
						else if(response.totalUpOrDown<0){
							this.scoreText=[`本次考试<span style="color: #E06C75;font-weight:bold;font-size:18px;">【${response.testPaperCode}】</span>，获得<span style="color: #E06C75;font-weight:bold;font-size:18px;">${response.totalScore}</span>分，排名小滑，但别担心，偶尔滑倒不是事儿，拍拍土，咱们继续攀登知识高峰！`]
						}
						else{
							this.scoreText=[`<span style="color: #E06C75;font-weight:bold;font-size:18px;">【${response.testPaperCode}】</span>成绩稳稳当当，获得<span style="color: #E06C75;font-weight:bold;font-size:18px;">${response.totalScore}</span>分，排名没变。不过老铁，世界那么大，要不要试着让分数和排名来点新风景？`]
						}
						console.log(response)
						switch (response.scoreClass) {
							case '管理类':
								this.GZList = [
									['英语', response.english, response.englishRanking, response.englishUpOrDown],
									['数学', response.maths, response.mathsRanking, response.mathsUpOrDown],
									['逻辑', response.logic, response.logicRanking, response.logicUpOrDown],
									['写作', response.writing, response.writingRanking, response.writingUpOrDown]
								]

								break;
							case '政英':
							    this.ZYList = [
									['英语',response.english, response.englishRanking, response.englishUpOrDown],
									['政治',response.politics, response.politicsRanking, response.politicsUpOrDown]
								]
								break;
							case '政数英':
							    this.ZSYList = [
									['英语',response.english, response.englishRanking, response.englishUpOrDown],
									['数学', response.maths, response.mathsRanking, response.mathsUpOrDown],
									['政治',response.politics, response.politicsRanking, response.politicsUpOrDown]
								]
								break;
							case '经济类':
							this.JJList=[
								['英语',response.english, response.englishRanking, response.englishUpOrDown],
								['政治',response.politics, response.politicsRanking, response.politicsUpOrDown],
								['数学', response.maths, response.mathsRanking, response.mathsUpOrDown],
								['逻辑', response.logic, response.logicRanking, response.logicUpOrDown],
								['写作', response.writing, response.writingRanking, response.writingUpOrDown]
							]
								
							default:
								break;
						}
						uni.hideLoading()

					} else {
						console.error('获取成绩细则失败:');
						uni.hideLoading()
						uni.showToast({
							title: '获取成绩细则失败',
							icon: 'error',
						});
					}
				} catch (error) {
					console.error('请求失败:', error);
					uni.hideLoading()
					uni.showToast({
						title: '请求失败',
						icon: 'error',
					});
				}
			},

		}
	}
</script>

<style lang="scss">
	page {}
	.ab-tr{
		position: absolute;
		right: 10px;
		top: 10px;
		font-weight: bold;
		
	}
	.title-style {
		font-size: 20px;
		font-family: fangsong;
		font-weight: bold;
		color: #777;
	}

	.span-title {
		font-weight: bold;
		font-size: 16px;
	}

	.courseMy {
		font-weight: bold;
		color: #617C96;
	}

	.span-title-2 {
		color: #EC4D4D;
		font-weight: bold;
		font-family: cursive;
	}

	.page {
		padding-bottom: 20rpx;
		min-height: 100vh;
		background: linear-gradient(to bottom, #1989FA, rgba(25, 137, 250, 0));

		.header {
			display: flex;
			flex-direction: column;
			color: #fff;
			padding: 25rpx;
			position: relative;

			.header-icon {
				position: absolute;
				top: 25rpx;
				right: 25rpx;
				width: 160rpx;
				height: 160rpx;
			}

			.header-title {
				font-size: 40px;
				font-weight: bold;
				margin-bottom: 20rpx;
			}

			.header-date {
				font-size: 12px;
				color: #d6d6d6;
			}
		}

		.ranking {
			width: 700rpx;
			border-radius: 30rpx;
			margin: auto;
			background: #fff;
			box-sizing: border-box;
			padding: 20rpx;
			margin-bottom: 4%;

			.rangking-title {
				display: flex;
				font-size: 14px;
				color: #555;
				height: 50rpx;
				line-height: 50rpx;

				text {
					display: block;
					width: 175rpx;
					text-align: center;

					&:nth-child(2) {
						box-sizing: border-box;
						padding-left: 60rpx;
						text-align: left;
						width: calc(100% - 375rpx);
					}

					&:nth-child(3) {
						width: 200rpx;
					}
				}
			}

			.ranking-list-item {
				height: 110rpx;
				display: flex;
				align-items: center;
				font-size: 14px;
				border-bottom: 1px solid #f0f0f0;

				.ranking-list-number {
					width: 175rpx;
					color: #777;
					display: flex;
					text-align: left;
					justify-content: center;

					image {
						width: 50rpx;
						height: 50rpx;
					}
				}

				.ranking-list-score {
					display: block;
					width: 200rpx;
					color: #E28935;
					font-size: 16px;
					text-align: center;
				}

				.ranking-list-nickname {
					display: flex;
					align-items: center;
					width: calc((100% - 350rpx)/2);
					box-sizing: border-box;
					// padding-left: 60rpx;
					justify-content: center;

					image {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
						margin-right: 20rpx;
					}

					text {
						width: auto;
					}
				}
			}
		}
	}
</style>