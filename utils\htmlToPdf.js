// 导出页面为PDF格式
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default{
  install (Vue, options) {
	  Vue.prototype.getPdf = function () {
	    const element = document.querySelector('#pdfDom');
	    const title = this.htmlTitle;
	    
	    // 优化配置项
	    const html2canvasOptions = {
	      useCORS: true,       // 支持跨域图片
	      scale: 2,            // 提升清晰度
	      logging: false,      // 关闭日志
	      windowHeight: element.scrollHeight // 确保捕获完整高度
	    };
	  
	    // 强制单页配置
	    const pdfOptions = {
	      orientation: 'p',    // 方向（p-纵向，l-横向）
	      unit: 'px',          // 使用像素单位
	      format: [element.offsetWidth, element.offsetHeight] // 自定义尺寸
	    };
	  
	    html2Canvas(element, html2canvasOptions).then(canvas => {
	      const imgWidth = canvas.width;
	      const imgHeight = canvas.height;
	      
	      // 创建与内容等大的PDF
	      const PDF = new JsPDF({
	        ...pdfOptions,
	        format: [imgWidth, imgHeight]
	      });
	  
	      // 添加图片（完整尺寸）
	      PDF.addImage(canvas, 'PNG', 0, 0, imgWidth, imgHeight);
	      
	      // 保存PDF
	      PDF.save(`${title}.pdf`);
	    });
	  }

	  }
}

