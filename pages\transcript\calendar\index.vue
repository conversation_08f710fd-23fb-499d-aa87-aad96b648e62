<template>
	<view>
		<uni-search-bar v-show="isTeacher" @confirm="onSearchConfirm" @cancel="cancel" @input="onSearchInput"
			:focus="true" v-model="userName" placeholder="请输入姓名" />
		<view v-if="showSuggestions" class="suggestion-container">
			<view v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item"
				@click="selectSuggestion(suggestion)">
				{{ suggestion.userName }}
			</view>
		</view>
		<view class="calendar-container">
			<uni-calendar class="custom-calendar" :show-mark="false" :selected="selected" :showMonth="false"
				@monthSwitch="monthSwitch" @change="" />
		</view>
	</view>
</template>

<script>
	import {
		create
	} from 'domain';
	import constant from '../../../utils/constant';
	import storage from '../../../utils/storage';
	import request from '@/utils/request';
	import {
		checkRole
	} from '@/utils/permission.js';
	import {
		getToken
	} from '@/utils/auth'
	import {
		getUserList
	} from '@/api/system/user.js';
	import {
		log
	} from 'util';

	export default {
		data() {
			return {
				selected: [],
				//selected: [{"date":"2025-03-22","info":[{"text":"英语","color":"#e74c3c"},{"text":"迟到","color":"#3498db"},{"text":"缺勤","color":"#2ecc71"}]}],
				searchDate: this.getCurrentDate(),
				userName: '',
				userId: '',
				isTeacher: false,
				suggestions: [], // 存储姓名建议
				showSuggestions: false, // 控制建议列表的显示
				debounceTimer: null, // 用于防抖的定时器
			};
		},
		mounted() {
			// // 判断用户角色是管理员或教师时设置isTeacher为true
			// const userRoles = storage.get(constant.roles) || []
			// this.isTeacher = userRoles.includes('管理员') || userRoles.includes('教师')
			// console.log('判断用户角色',this.isTeacher);
			//上面的是错误写法，/utils/permission.js和/plugins/auth.js已经定好了权限和角色的校验方法，全部有关校验修改下面成写法（我已把原来的'admin'和'teacher'改成'管理员'和'教师'）
			this.isTeacher = checkRole(['管理员', '教师']);

			console.log('判断用户角色', this.isTeacher);
		},
		created() {
			console.log('create');

			this.$nextTick(() => {
				// 点击月份
				let prevBtn = document.querySelector('.uni-calendar__header-btn-box');
				console.log(prevBtn, 'prevBtn');

				prevBtn.addEventListener('click', () => {
					this.monthSwitch();
				});
			});
		},

		onLoad() {
			
			if(!this.isTeacher){
				this.fetchData();
			}
		},
		methods: {
			getCurrentDate() {
				// 创建 Date 对象
				const date = new Date();
				// 获取年份
				const year = date.getFullYear();
				// 获取月份并加 1
				const month = String(date.getMonth() + 1).padStart(2, '0');
				// 拼接日期
				return `${year}-${month}`;
			},
			// 处理搜索框输入事件，用于获取姓名建议
			onSearchInput(e) {
				console.log('搜索输入:', e);
				// 清除之前的定时器
				if (this.debounceTimer) {
					clearTimeout(this.debounceTimer);
				}

				// 设置新的定时器，实现防抖
				this.debounceTimer = setTimeout(() => {
					const value = e; // 直接使用传入的值而不是this.userName
					if (value && value.length > 0) {
						this.fetchUserNameSuggestions(value);
					} else {
						this.suggestions = [];
						this.showSuggestions = false;
					}
				}, 300); // 300ms的防抖延迟
			},

			// 请求姓名建议列表
			async fetchUserNameSuggestions(name) {
				console.log('开始获取建议, 输入值:', name);
				if (!name || name.trim() === '') {
					this.suggestions = [];
					this.showSuggestions = false;
					return;
				}
				//代码优化
				getUserList(name, 10)
					.then((response) => {
						console.log('姓名建议:', response);
						if (response && Array.isArray(response)) {
							this.suggestions = response;
							this.showSuggestions = this.suggestions.length > 0;
						} else {
							this.suggestions = [];
							this.showSuggestions = false;
						}
					})
					.catch((error) => {
						console.error('获取姓名建议失败:', error);
						this.suggestions = [];
						this.showSuggestions = false;
					});


			},

			// 选择一个姓名建议
			selectSuggestion(suggestion) {
				console.log('555', suggestion);
				this.userName = suggestion.userName;
				this.userId = suggestion.id;
				this.showSuggestions = false;
				this.fetchData();
			},
			//编写一个函数用于转化searchDate
			getMonthRange(monthStr) {
				// 解析输入字符串
				const [year, month] = monthStr.split('-').map(Number)

				// 创建当月第一天（注意月份从0开始）
				const firstDay = new Date(year, month - 1, 1)

				// 创建下个月的第0天（即当月的最后一天）
				const lastDay = new Date(year, month, 0)

				// 格式化日期函数
				const formatDate = date => {
					const y = date.getFullYear()
					const m = date.getMonth() + 1 // 转换为1-12
					const d = date.getDate()
					return `${y}/${m}/${d} 00:00:00` // 强制时间部分为00:00:00
				}

				return {
					firstDay: formatDate(firstDay),
					lastDay: formatDate(lastDay)
				}
			},
			// 请求后端 API 获取打卡数据
			async fetchData() {
				const {
					firstDay,
					lastDay
				} = this.getMonthRange(this.searchDate)
				try {

					let id = ''
					await this.$store.dispatch('GetInfo').then((res) => {
						console.log('获取用户信息结果', res.id);

						id = res.id
					});

					if (this.userId) {
						console.log('获取用户信息结果', this.userId);
						id = this.userId;
					}
					console.log('id:' + id);
					uni.showLoading({
						title: '加载中'
					})
					const response = await request({
						url: '/ApiCaAttend/GetList',
						Headers: {
							'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
						},
						method: 'POST',
						data: {
							beginTime: firstDay.toString(),
							endTime: lastDay.toString(),
							userId: id,
						},
					});
					console.log("response", response);

					if (response.length > 0) {
						// 转换接口返回的数据格式
						this.selected = [];

						if (response && Array.isArray(response)) {
							response.forEach((item) => {
								let infoArray = [item.course, item.morningStatus, item.afternoonStatus];

								if (infoArray) {
									// 确保至少有3个标签，便于展示上午和下午标签
									// let infoArray = Array.isArray(infoArray) ? [...infoArray] : [infoArray];

									// 如果数组长度小于3，补充元素
									// while (infoArray.length < 3) {
									// 	if (infoArray.length === 1) {
									// 		infoArray.push('课程'); // 第二个元素（上午）
									// 	} else if (infoArray.length === 2) {
									// 		infoArray.push('活动'); // 第三个元素（下午）
									// 	}
									// }
									console.log('infoArray', infoArray)
									// 转换为对象数组，添加颜色
									const formattedInfo = infoArray.map((text, index) => {
										// 根据文本内容确定颜色
										let color;

										// 判断关键词分配颜色
										if (text.includes('迟到')) {
											color = '#ff0000'; // 迟到：红色
										} else if (text.includes('旷课')) {
											color = '#ff69b4'; // 旷课：粉红色
										} else if (text.includes('请假')) {
											color = '#ffa500'; // 请假：橙色
										} else if (text.includes('出勤')) {
											color = '#4caf50'; // 出勤：绿色
										} else if (index === 0) {
											color = '#000'; // 第一个标签默认颜色
										} else if (index === 1) {
											color = '#80bfe8'; // 第二个标签默认颜色
										} else if (index === 2) {
											color = '#abe4b8'; // 第三个标签默认颜色
										} else {
											// 如果有更多标签，使用默认颜色
											const defaultColors = ['#f39c12', '#9b59b6', '#1abc9c'];
											color = defaultColors[(index - 3) % defaultColors.length];
										}
										console.log('color', color)

										return {
											text: text,
											color: color,
										};
									});

									// 添加到selected数组
									this.selected.push({
										date: item.checkInDate,
										info: formattedInfo,
									});
									uni.hideLoading()
									console.log('this.selected', this.selected)
								}
							});
						}

						// 如果没有数据，添加一个演示数据
						if (this.selected.length === 0) {
							const today = new Date();
							const year = today.getFullYear();
							const month = String(today.getMonth() + 1).padStart(2, '0');

							// 当前日期测试数据
							const currentDay = String(today.getDate()).padStart(2, '0');
							const todayStr = `${year}-${month}-${currentDay}`;

							// 多添加几个不同日期的测试数据
							this.selected = [];
						}

						console.log('处理后的数据:', this.selected);
						
					} else {
						console.error('获取打卡数据失败:', response.data);
						uni.showToast({
							title: '获取打卡数据失败',
							icon: 'none',
						});
					}
				} catch (error) {
					console.error('请求失败:', error);
					uni.showToast({
						title: '请求失败',
						icon: 'none',
					});
				}
			},
			monthSwitch(e) {
				// if(e.month.length===1){
				// 	e.month='0'+e.month
				// }
				console.log('monthSwitchs 返回:', e);
				this.searchDate = e.year + '-' + String(e.month).padStart(2, '0');
				this.fetchData();
			},
			onSearchConfirm(res) {
				this.userName = res.value;
				this.showSuggestions = false;
				console.log('执行搜索:', res.value);
				this.fetchData();
			},
			cancel() {
				this.userName = '';
				this.suggestions = [];
				this.showSuggestions = false;
				this.fetchData();
			},
		},
	};
</script>

<style scoped>
	.calendar-container {
		padding: 15px 8px;
		background: linear-gradient(135deg, #f8f9fc, #f1f4f9);
		perspective: 1200px;
		/* 增强透视效果 */
		min-height: 85vh;
	}

	.custom-calendar {
		border-radius: 18px;
		box-shadow: 0 20px 40px rgba(50, 50, 93, 0.15), 0 8px 20px rgba(0, 0, 0, 0.1);
		background-color: #ffffff;
		overflow: hidden;
		transform: rotateX(3deg) rotateY(1deg);
		/* 更立体的3D倾斜效果 */
		transform-style: preserve-3d;
		transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
		position: relative;
		backdrop-filter: blur(8px);
	}

	.custom-calendar:hover {
		transform: rotateX(0deg) rotateY(0deg) translateY(-8px);
		box-shadow: 0 25px 50px rgba(50, 50, 93, 0.2), 0 12px 24px rgba(0, 0, 0, 0.15);
	}

	/* 日历条纹背景 */
	.custom-calendar::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: repeating-linear-gradient(45deg, rgba(245, 247, 250, 0.5), rgba(245, 247, 250, 0.5) 12px, rgba(235, 240, 245, 0.5) 12px, rgba(235, 240, 245, 0.5) 24px);
		z-index: 0;
		border-radius: 18px;
		opacity: 0.6;
	}

	/* 姓名建议列表样式 */
	.suggestion-container {
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 14px;
		margin: 0 15px;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
		max-height: 200px;
		overflow-y: auto;
		z-index: 100;
		position: relative;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.8);
		transform: translateY(5px);
		animation: fadeIn 0.3s ease-out forwards;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10px);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.suggestion-item {
		padding: 14px 18px;
		border-bottom: 1px solid rgba(240, 240, 240, 0.8);
		font-size: 14px;
		transition: all 0.25s ease;
		position: relative;
		overflow: hidden;
	}

	.suggestion-item:last-child {
		border-bottom: none;
	}

	.suggestion-item:active {
		background-color: rgba(245, 247, 250, 0.8);
	}

	.suggestion-item::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 0;
		background: linear-gradient(90deg, #6a89cc, #4a69bd);
		z-index: -1;
		transition: height 0.25s ease;
		opacity: 0;
	}

	.suggestion-item:hover {
		color: #4a69bd;
		padding-left: 24px;
	}

	.suggestion-item:hover::after {
		height: 3px;
		opacity: 1;
	}

	.custom-calendar :deep(.uni-calendar__header) {
		height: 75px;
		background: linear-gradient(135deg, #5878cf, #3a5cbd);
		position: relative;
		z-index: 1;
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.custom-calendar :deep(.uni-calendar__header::after) {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 1px;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	}

	.custom-calendar :deep(.uni-calendar__header-text) {
		color: #ffffff;
		font-size: 20px;
		font-weight: bold;
		text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
		letter-spacing: 1px;
	}

	.custom-calendar :deep(.uni-calendar__header-btn) {
		border-color: rgba(255, 255, 255, 0.8);
		opacity: 0.9;
		transition: all 0.3s ease;
		width: 32px;
		height: 32px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.custom-calendar :deep(.uni-calendar__header-btn:hover) {
		opacity: 1;
		background-color: rgba(255, 255, 255, 0.15);
		transform: scale(1.05);
	}

	.custom-calendar :deep(.uni-calendar__backtoday) {
		background-color: rgba(255, 255, 255, 0.2);
		color: #ffffff;
		font-weight: bold;
		border-radius: 24px;
		padding: 6px 14px;
		box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
		transition: all 0.3s ease;
		border: 1px solid rgba(255, 255, 255, 0.3);
		letter-spacing: 0.5px;
	}

	.custom-calendar :deep(.uni-calendar__backtoday:hover) {
		background-color: rgba(255, 255, 255, 0.3);
		box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
		transform: translateY(-2px);
	}

	.custom-calendar :deep(.uni-calendar__weeks-day) {
		height: 60px;
		border-bottom: 1px solid rgba(238, 242, 247, 0.8);
		position: relative;
		z-index: 1;
		background-color: rgba(255, 255, 255, 0.9);
		transition: background-color 0.3s ease;
	}

	.custom-calendar :deep(.uni-calendar__weeks-day-text) {
		font-weight: 600;
		color: #3d4852;
		font-size: 14px;
		letter-spacing: 0.5px;
	}

	.custom-calendar :deep(.uni-calendar-item__weeks-box) {
		border: none !important;
		height: 130px;
		/* 进一步增加高度以提供更多空间 */
		transition: all 0.35s cubic-bezier(0.2, 0.8, 0.2, 1);
		position: relative;
		z-index: 1;
		background-color: rgba(255, 255, 255, 0.8);
	}

	.custom-calendar :deep(.uni-calendar-item__weeks-box:hover) {
		background-color: rgba(240, 245, 255, 0.85);
		transform: translateZ(8px) scale(1.02);
		z-index: 2;
		box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
	}

	.custom-calendar :deep(.uni-calendar-item__weeks-box-text) {
		font-size: 17px;
		font-weight: 600;
		position: relative;
	}

	.custom-calendar :deep(.uni-calendar-item--isDay) {
		background: linear-gradient(135deg, #5878cf, #3a5cbd);
		border-radius: 50%;
		width: 42px;
		height: 42px;
		line-height: 42px;
		text-align: center;
		opacity: 1;
		box-shadow: 0 5px 12px rgba(74, 105, 189, 0.35);
		position: relative;
		overflow: hidden;
	}

	.custom-calendar :deep(.uni-calendar-item--isDay::before) {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transform: translateX(-100%);
		animation: shimmer 2s infinite;
	}

	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}

	.custom-calendar :deep(.uni-calendar-item--checked) {
		background: linear-gradient(135deg, #5878cf, #3a5cbd);
		border-radius: 50%;
		width: 42px;
		height: 42px;
		line-height: 42px;
		text-align: center;
		opacity: 1;
		box-shadow: 0 5px 12px rgba(74, 105, 189, 0.35);
		transform: translateZ(4px);
		position: relative;
		overflow: hidden;
	}

	.custom-calendar :deep(.uni-calendar-item--checked::before) {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
	}

	.custom-calendar :deep(.uni-calendar-item__weeks-lunar-text) {
		font-size: 11px;
		margin-top: 4px;
		opacity: 0.75;
		color: #5d6778;
	}

	/* 美化标签的样式 */
	.custom-calendar :deep(.tag-container) {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 8px;
		position: relative;
		z-index: 2;
	}

	.custom-calendar :deep(.tag-wrapper) {
		width: 90%;
		min-width: 65px;
		display: flex;
		flex-direction: row;
		align-items: center;
		margin: 4px 0;
		position: relative;
	}

	.custom-calendar :deep(.tag-item) {
		flex: 1;
		padding: 3px 8px;
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
		transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
		position: relative;
		border: 1px solid rgba(255, 255, 255, 0.5);
		backdrop-filter: blur(4px);
	}

	.custom-calendar :deep(.tag-item:hover) {
		transform: translateY(-2px) translateZ(8px);
		box-shadow: 0 5px 10px rgba(0, 0, 0, 0.12);
	}

	.custom-calendar :deep(.tag-item::after) {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 40%;
		background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), transparent);
		border-radius: 8px 8px 0 0;
	}

	.custom-calendar :deep(.tag-text) {
		overflow: hidden;
		text-overflow: ellipsis;
		text-align: left;
		font-size: 11px !important;
		font-weight: 500;
		position: relative;
		z-index: 1;
		padding: 0 2px;
	}

	.custom-calendar :deep(.tag-prefix) {
		width: 16px;
		/* 增加前缀宽度 */
		text-align: center;
		font-size: 11px;
		font-weight: 600;
		color: #3d4852;
		position: relative;
		z-index: 1;
	}

	.custom-calendar :deep(.no-border) {
		border: none !important;
		background-color: transparent !important;
		justify-content: center;
		width: 100%;
		margin-top: 2px;
	}

	.custom-calendar :deep(.no-border .tag-text) {
		text-align: center;
		font-weight: 600;
		font-size: 13px !important;
		color: #2d3748 !important;
		position: relative;
		letter-spacing: 0.5px;
	}

	.custom-calendar :deep(.no-border .tag-text::after) {
		content: '';
		position: absolute;
		bottom: -3px;
		left: 50%;
		transform: translateX(-50%);
		width: 85%;
		height: 1px;
		background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
	}

	.custom-calendar :deep(.first-tag-wrapper) {
		width: 90%;
		justify-content: center;
		align-items: center;
		margin-bottom: 8px;
		position: relative;
	}

	.custom-calendar :deep(.first-tag-wrapper::before) {
		content: '';
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		height: 1px;
		background: linear-gradient(90deg, transparent, rgba(120, 144, 240, 0.2), transparent);
	}

	.custom-calendar :deep(.morning) {
		box-shadow: 0 3px 8px rgba(106, 137, 204, 0.15);
		background: linear-gradient(to right, rgba(240, 244, 255, 0.8), rgba(225, 235, 250, 0.8));
	}

	.custom-calendar :deep(.afternoon) {
		box-shadow: 0 3px 8px rgba(171, 228, 184, 0.15);
		background: linear-gradient(to right, rgba(230, 250, 240, 0.8), rgba(210, 245, 225, 0.8));
	}

	/* 美化搜索栏 */
	:deep(.uni-searchbar) {
		padding: 16px 18px;
		background: linear-gradient(to right, #f0f4f9, #f8fafd);
		box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
		border-radius: 0 0 20px 20px;
		border-bottom: 1px solid rgba(220, 230, 240, 0.8);
	}

	:deep(.uni-searchbar__box) {
		border-radius: 22px;
		height: 44px;
		box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
		border: 1px solid rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(8px);
		background-color: rgba(255, 255, 255, 0.9);
		transition: all 0.3s ease;
	}

	:deep(.uni-searchbar__box:focus-within) {
		box-shadow: 0 5px 15px rgba(50, 50, 93, 0.1), 0 3px 8px rgba(0, 0, 0, 0.07);
		transform: translateY(-1px);
	}

	:deep(.uni-searchbar__text-input) {
		color: #3d4852;
		font-size: 15px;
		letter-spacing: 0.5px;
	}

	:deep(.uni-searchbar__cancel) {
		color: #5878cf;
		font-size: 15px;
		font-weight: 600;
	}

	:deep(.uni-icons) {
		color: #6a89cc;
	}
</style>