<template>
	<view class="container">
		</image>
		<scroll-view class="button-container" scroll-y="true">
			<view class="button-item " v-for="(item, index) in buttons" :key="index"
				@click="navigateTo(item.text)">
				<!-- 使用图片代替文字 -->
				<image class="button-image clickPng" :src="item.isPressed ? item.pressedImage : item.defaultImage"
					mode="widthFix"></image>
			</view>
		</scroll-view>

		<!-- 科目类型选择器 - 隐藏式弹窗 -->
		<uni-data-picker ref="subjectPicker" :placeholder="titleText" :popup-title="titleText" v-model="selectedSubject"
			:localdata="subjectData" :show="false" @change="onSubjectChange" @nodeclick="onSubjectNodeClick"
			@popupopened="onSubjectPopupOpened" @popupclosed="onSubjectPopupClosed">
		</uni-data-picker>
	</view>
</template>

<script>
	import request from '@/utils/request'

	export default {
		data() {
			return {
				// 6个大栏目按钮数据
				buttons: [{
						defaultImage: '/static/images/01.png',
						text: '历史真题'
					},
					{
						defaultImage: '/static/images/03.png',
						text: '巩固练习'
					},
					{
						defaultImage: '/static/images/05.png',
						text: '随机练习'
					},
				],
				// 科目选择器相关数据
				selectedSubject: '',
				subjectData: [],
				titleText: '',
				// 新增变量记录当前操作类型
				currentActionType: '',
			}
		},
		computed: {
			dynamicEvent() {
				return 'click';
			}
		},
		methods: {
			// 导航到指定页面
			navigateTo( text) {
				// 获取科目数据并显示弹窗选择器
				this.titleText = text
				this.currentActionType = text;
				this.fetchSubjectTypes();
			},

			// 获取科目类型数据
			async fetchSubjectTypes() {
				try {
					const response = await request({
						url: '/ApiCaSubject/GetType',
						method: 'POST',
					});

					if (response) {
						// 转换数据格式为 uni-data-picker 需要的格式
						this.subjectData = this.convertSubjectData(response);
						// 数据加载完成后显示弹窗
						this.$nextTick(() => {
							this.$refs.subjectPicker.show();
						});
					} else {
						uni.showToast({
							title: '获取科目类型失败',
							icon: 'error'
						});
					}
				} catch (error) {
					console.error('获取科目类型失败:', error);
					uni.showToast({
						title: '获取科目类型失败',
						icon: 'error'
					});
				}
			},

			// 转换科目数据格式为级联结构
			convertSubjectData(data) {
				// 过滤显示的数据
				const filteredData = data.filter(item => item.isShow);

				// 构建层级关系
				const buildTree = (items, parentId = 0) => {
					return items
						.filter(item => item.pId === parentId)
						.sort((a, b) => a.order - b.order)
						.map(item => {
							const node = {
								text: item.name,
								value: item.id.toString()
							};

							// 查找子节点
							const children = buildTree(items, item.id);
							if (children.length > 0) {
								node.children = children;
							}

							return node;
						});
				};

				return buildTree(filteredData);
			},

			// 科目选择器事件处理
			onSubjectChange(e) {
				console.log('科目选择变化:', e);
				this.selectedSubject = e;
				
				// 获取选择的科目ID
				let selectedId;
				if (Array.isArray(e.detail.value)) {
					selectedId = e.detail.value[e.detail.value.length - 1].value;
				} else {
					selectedId = e;
				}

				// 只有选择了具体的科目才跳转（排除巩固练习和随机练习）
				if (selectedId && this.currentActionType === '历史真题') {
					// 手动关闭选择器
					this.$refs.subjectPicker.hide();
					
					uni.navigateTo({
						url: `/pages/examination/topic/index?exercisesType=${this.currentActionType}&rId=${selectedId}`
					});
				}
			},

			onSubjectNodeClick(e) {
				console.log('科目节点点击:', e);
				// 当选择科目且是"巩固训练"或"随机训练"时，一级菜单就跳转
				if (this.currentActionType === '巩固练习' || this.currentActionType === '随机练习') {
					// 手动关闭选择器
					this.$refs.subjectPicker.hide();
					
					// 根据当前操作类型确定跳转路径
					const targetPath = this.currentActionType === '巩固练习' 
						? '/pages/transcript/score/index' 
						: '/pages/examination/index';
					
					uni.navigateTo({
						url: `/pages/examination/topic/index?exercisesType=${this.currentActionType}&rId=${e.value}`
					});
				}
			},

			onSubjectPopupOpened() {
				console.log('科目选择器打开');
			},

			onSubjectPopupClosed() {
				console.log('科目选择器关闭');
			}
		},
	};
</script>

<style>
	.clickPng:hover,
	.clickPng:active {
		filter: brightness(96%);
	}

	/deep/ .input-value-border {
		display: none !important;
	}

	/* 页面容器 */
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #FFFFFF;
	}

	/* 按钮容器 */
	.button-container {
		border-radius: 20px;
		padding: 5px;
		margin-top: 4vh;
		height: 100vh;
		width: 96%;
		max-width: 600px;
		background-color: #FFFFFF;
	}

	/* 单个按钮 */
	.button-item {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		margin: 20px 0;
		border-radius: 12px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}

	.button-item:active {
		transform: scale(0.95);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/* 按钮图片 */
	.button-image {
		width: 100%;
		height: 155px;
		border-radius: 8px;
	}
</style>
