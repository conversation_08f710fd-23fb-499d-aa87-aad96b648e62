<style>
	.container {
		display: flex;
		flex-direction: column;
		justify-content: center;
		width: 100vw;
	}

	.title {
		display: flex;
		justify-content: space-between;
		margin-top: 10rpx;
		font-size: 30rpx;
		font-weight: 700;
		padding: 0rpx 50rpx;
		height: 25px;
	}

	.title uni-list-item {
		background-color: #C0C0C0;
	}

	.title view {
		transition: 0.5s;
	}

	.active {
		position: relative;
		color: #dd1705;
		font-size: 31rpx;
		font-weight: 700;
	}

	.active:before {
		content: "";
		position: absolute;
		width: 20rpx;
		height: 8rpx;
		background-color: #dd1705;
		top: 50rpx;
		left: 35%;
		border-radius: 15rpx;
	}

	.get_job {
		padding: 0rpx 20rpx;
		display: flex;
		align-items: center;
		margin: 20rpx 0rpx;
	}

	.get_job text:nth-child(1) {
		font-size: 35rpx;
		font-weight: 700;
		text-align: left;
	}

	.get_job text:nth-child(2) {
		margin-left: 40rpx;
		font-size: 25rpx;
		color: #808080;
	}

	.technology {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		box-shadow: 0rpx 2rpx 10rpx 0rpx #F0F0F0;
		padding: 20rpx 20rpx;
		margin: 0rpx 20rpx;
		border-radius: 10rpx;
	}

	.java,
	.html,
	.css,
	.javascipt {
		text-align: center;
		width: 155rpx;
		height: 100rpx;
		line-height: 100rpx;
		background-image: linear-gradient(45deg, rgb(6, 121, 252), rgb(120, 163, 228));
		border-radius: 10%;
		color: white;
		box-shadow: 3rpx 5rpx 10rpx 0rpx #CCCCCC;
		font-size: 26rpx;
	}

	.job_market {
		margin-top: 40rpx;
		padding: 0rpx 20rpx;
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.job_market text:nth-child(1) {
		font-size: 35rpx;
		font-weight: 700;
		text-align: left;
	}

	.job_market text:nth-child(2) {
		margin-left: 40rpx;
		font-size: 25rpx;
		color: #808080;
	}

	.advance {
		display: flex;
		justify-content: space-between;
		padding: 20rpx;
		margin: 0rpx 20rpx;
		box-shadow: 0rpx 2rpx 10rpx 0rpx #F0F0F0;
		border-radius: 10rpx;
	}

	.advance view {
		display: flex;
		align-items: center;
		text-align: center;
		width: 200rpx;
		height: 150rpx;
		color: white;
		font-size: 26rpx;
		padding: 20rpx;
		box-sizing: border-box;
		border-radius: 8%;
		box-shadow: 0rpx 10rpx 10rpx 0rpx #F0F0F0;
	}

	.advance view:nth-child(1) {
		background-image: linear-gradient(110deg, rgb(81, 142, 212), rgb(4, 89, 248));
	}

	.advance view:nth-child(2) {
		background-image: linear-gradient(rgb(255, 72, 0), rgb(230, 104, 46));
	}

	.advance view:nth-child(3) {
		background-image: linear-gradient(rgb(150, 4, 247), rgb(144, 65, 235));
	}

	.hot_free {
		margin: 40rpx 0rpx 20rpx 0rpx;
		padding: 0rpx 20rpx;
		display: flex;
		align-items: center;
	}

	.hot_free text:nth-child(1) {
		font-size: 35rpx;
		font-weight: 700;
		text-align: left;
	}

	.hot_free text:nth-child(2) {
		margin-left: 40rpx;
		font-size: 25rpx;
		color: #808080;
	}

	.free_curriculum {
		padding: 20rpx;
		margin: 30rpx 30rpx 0rpx 20rpx;
		box-shadow: 0rpx 5rpx 10rpx 0rpx #F0F0F0;
		border-radius: 10rpx;
	}

	.free_curriculum image {
		width: 180rpx;
		height: 180rpx;
		float: left;
		margin-right: 20rpx;
		box-shadow: 0rpx 0rpx 5rpx 5rpx #dddddd;
		border-radius: 10rpx;
	}

	.free_curriculum view:nth-of-type(1) {
		font-weight: 700;
		font-size: 30rpx;
	}

	.free_curriculum view:nth-of-type(2),
	.free_curriculum view:nth-of-type(3) {
		color: #808080;
		font-size: 25rpx;
		margin-top: 20rpx;
	}

	.free_curriculum .icon text {
		margin: 0rpx 5rpx 0rpx 10rpx;
	}

	.pictrue {
		margin-top: 20rpx;
	}

	.pictrue swiper-item {
		width: 90%;
		height: 90%;
		display: flex;
		justify-content: center;
	}

	.pictrue image {
		width: 90%;
		height: 90%;
		box-shadow: 0rpx 20rpx 10rpx 0rpx #c7c7c7;
		border-radius: 15rpx;
	}

	input {
		background-color: #ebebeb;
		height: 70rpx;
		padding-left: 20rpx;
		margin: 0rpx 30rpx;
		border-radius: 10rpx;
		margin-top: 10rpx;
	}

	input[placeholder] {
		font-size: 28rpx;
	}
</style>
<template>
	<view class="container">
		<wsx-tab v-model="value" :options="list" @change="change"></wsx-tab>
		<view class="hot_free">
			<text>课程视频</text>
			<text>开启您的升学之路</text>
		</view>
			<i-video-list  :id="idList" v-show="idList.length>0"/>
		<!-- <view class="free_curriculum" @click="to_video">
			<image src="" mode=""></image>
			<view class="">初识Phthon</view>
			<view class="icon">入门 <text class="icon-renwu"></text>213241 21321人评价</view>
			<view class="">免费</view>
		</view>
		<view class="free_curriculum">
			<image src="" mode=""></image>
			<view class="">初识Phthon</view>
			<view class="icon">入门 <text class="icon-renwu"></text>213241 21321人评价</view>
			<view class="">免费</view>
		</view>
		<view class="free_curriculum">
			<image src="" mode=""></image>
			<view class="">初识Phthon</view>
			<view class="icon">入门 <text class="icon-renwu"></text>213241 21321人评价</view>
			<view class="">免费</view>
		</view>
		<view class="free_curriculum">
			<image src="" mode=""></image>
			<view class="">初识Phthon</view>
			<view class="icon">入门 <text class="icon-renwu"></text>213241 21321人评价</view>
			<view class="">免费</view>
		</view> -->
		<o-empty v-show="idList.length<=0"/>
	</view>
</template>

<script>
	import {
		checkRole
	} from '@/utils/permission.js'
	import request from '@/utils/request'
	import storage from '../../utils/storage'
	import wsxTab from "@/components/wsx-tab/wsx-tab.vue"
	import iVideoList from "@/components/i-video-list/i-video-list.vue"
	export default {
		data() {
			return {
				userName: '', // 用户名称
				userNamekey: '', //搜索关键字
				index: 0,
				current: 0,
				autoplay: true,
				indicator_dots: true,
				interval: 3000,
				circular: true,
				indicator_active_color: "#353535",
				isTeacher: false,
				value: 0,
				list: [],
				courseId: 0,
				loading: true,
				idList:[]   //查询到的课程列表
			}
		},
		onLoad() {
			setTimeout(() => {
				this.loading = false
			}, 1000)
		},
		mounted() {
			this.isTeacher = checkRole(['管理员', '教师']);
			// if (this.isTeacher) {
				this.userName = ''
			// } else {
			// 	this.userName = storage.get(constant.name)
			// 	this.userNamekey = storage.get(constant.name)

			// }
			this.fetchData().then(res=>{
				this.getById()
			}); // 初始化加载数据
			
		},
		methods: {

			choice(index) {
				this.current = index,
					this.index = index
			},
			change(e1, e2) {
				console.log(e1, e2)
				this.courseId = e1
				this.getById()
			},
			//查询课程
			async getById() {
				try {
					const response = await request({
						url: '/ApiCaCourse/GetList',
						method: 'POST',
						data: {
							path: this.courseId
						},
					});
					if (response && response.length > 0) {
						this.idList = response.map((item, index) => {
							console.log(item.level)
					
					
							return item.id
					
						})
					
					
					} else {
					this.idList=[]
					}

				} catch (error) {
					console.error('请求失败:', error);
					uni.showToast({
						title: '请求失败',
						icon: 'error',
						duration: 1500
					})
				}
			},
			// 请求后端 API 获取数据
			async fetchData() {
				try {
					const response = await request({
						url: '/ApiCaCourse/GetListType',
						method: 'POST',
						data: {
							level: 1
						},
					});
					console.log("response", response);

					if (response && response.length > 0) {
						this.list = response.map((item, index) => {
							console.log(item.level)
							if(index==0){
								this.courseId=item.path
							}


							return {
								value: index,
								label: item.fullName,
								id: item.id,
								path: item.path
							}

						})


					} else {

					}
				} catch (error) {
					console.error('请求失败:', error);
					uni.showToast({
						title: '请求失败',
						icon: 'error',
						duration: 1500
					})
				}
			},
		}
	}
</script>