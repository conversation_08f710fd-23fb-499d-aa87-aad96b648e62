import store from '@/store'
import config from '@/config'
import {
	getToken
} from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import {
	toast,
	showConfirm,
	tansParams
} from '@/utils/common'

let timeout = 10000
const baseUrl = config.baseUrl

const download = config => {
	console.log('请求参数', config)
	// 是否需要设置 token
	const isToken = (config.headers || {}).isToken === false
	config.header = config.header || {}
	if (getToken() && !isToken) {
		console.log('请求Token:' + getToken())
		config.header['Authorization'] = 'Bearer ' + getToken()
	}
	if (config.params) {
		let url = config.url + '?' + tansParams(config.params)
		url = url.slice(0, -1)
		config.url = url
		console.log('请求params参数', config.params)
	}
	return new Promise((resolve, reject) => {
		uni.downloadFile({
			timeout: config.timeout || timeout,
			url: baseUrl + config.url,
			header: config.header,
			success: (result) => {
				console.log("请求结果:", result);
				const msg = errorCode[result.statusCode] || result.errMsg || errorCode[
					'default']
				if (result.statusCode == 200) {
					resolve(result)
				} else if (result.statusCode == 401) {
					showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then(res => {
						if (res.confirm) {
							store.dispatch('LogOut').then(res => {
								uni.reLaunch({
									url: '/pages/login/login'
								})
							})
						}
					})
					reject('无效的会话，或者会话已过期，请重新登录。')
				} else if (result.statusCode === 500) {
					toast(msg)
					reject('500')
				} else if (result.statusCode !== 200) {
					toast(msg)
					reject(result.statusCode)
				}
			},
			fail: (error) => {
				let {
					message
				} = error
				if (message == 'Network Error') {
					message = '后端接口连接异常'
				} else if (message.includes('timeout')) {
					message = '系统接口请求超时'
				} else if (message.includes('Request failed with status code')) {
					message = '系统接口' + message.substr(message.length - 3) + '异常'
				}
				toast(message)
				reject(error)
			}
		})
	})
}

export default download