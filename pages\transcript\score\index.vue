<template>
	<view class="container">

		<uni-search-bar v-show="isTeacher" @confirm="onSearchConfirm" @cancel="cancel" @input="onSearchInput"
			:focus="true" v-model="userName" placeholder="请输入姓名" />
		<view  v-show="isTeacher" v-if="showSuggestions" class="suggestion-container">
			<view v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item"
				@click="selectSuggestion(suggestion)">
				{{ suggestion.userName }}
			</view>
		</view>
		<!-- 表格 -->
		<scroll-view scroll-y="true" class="scroll-box" @scrolltolower="loadMore" lower-threshold="50">
			<view class="table-page">
				<view class="table">
					<view class="table-row table-header">
						<view class="table-cell">代号</view>
						<view class="table-cell">姓名</view>
						<view class="table-cell">总分</view>
						<view class="table-cell">排名</view>
						<view class="table-cell">升降</view>
						<view class="table-cell">详情</view>
					</view>
					<view class="table-row" v-for="(item, index) in dataList" :key="index">
						<view class="table-cell">{{ item.testPaperCode }}</view>
						<view class="table-cell">{{ item.userName }}</view>
						<view class="table-cell">{{ item.totalScore }}</view>
						<view class="table-cell">{{ item.totalRanking }}</view>
						<view class="table-cell">
							<view class="ranking-cell"  style="position: relative;height: 100%;font-weight: 400;">
								<text v-if="item.totalUpOrDown > 0" class="ranking-up"><uni-icons type="arrow-up" size="20" color="#E06C75" class="ab-tr"></uni-icons>{{Math.abs(item.totalUpOrDown)}}</text>
								<text v-else-if="item.totalUpOrDown < 0" class="ranking-down"><uni-icons type="arrow-down" size="20" color="#77C2FA" class="ab-tr"></uni-icons>{{Math.abs(item.totalUpOrDown)}}</text>
							</view>
						</view>
						<button style="font-size: 12px;background-color: #B386EF;font-weight:900;color: azure;" class="table-cell" @click="goToDetail(item.id)">明细</button>
					</view>
				</view>

				<!-- 加载提示 -->
				<view class="loading" v-if="loading">
					<text>加载中...</text>
				</view>
				<view class="no-more" v-if="noMore">
					<text>没有更多数据了</text>
				</view>
			</view>
			<uni-load-more :status="loadingStatus"></uni-load-more>
		</scroll-view>
	</view>
</template>

<script>
	import {
		log
	} from 'util';
	import constant from '../../../utils/constant'
	import storage from '../../../utils/storage'
	import request from '@/utils/request'
	import {
		checkRole
	} from '@/utils/permission.js'
	import {
		getUserList
	} from '@/api/system/user.js';

	export default {
		data() {
			return {
				userName: '', // 用户名称
				userNamekey: '', //搜索关键字
				dataList: [], // 表格数据
				page: 1, // 当前页码
				pageSize: 20, // 每页数据量
				loading: false, // 是否正在加载
				noMore: false, // 是否没有更多数据
				isScrollLoading: false, // 是否正在滚动加载
				suggestions: [], // 存储姓名建议
				showSuggestions: false, // 控制建议列表的显示
				isTeacher: false,
				loadingStatus: "more"
			};
		},
		mounted() {
			this.isTeacher = checkRole(['管理员', '教师']);
			if (this.isTeacher) {
				this.userName = ''
			} else {
				this.userName = storage.get(constant.name)
				this.userNamekey = storage.get(constant.name)
				this.fetchData(); // 初始化加载数据
			}

			

		},
		onLoad() {

		},
		methods: {
			// 选择一个姓名建议
			selectSuggestion(suggestion) {
				console.log('555', suggestion);
				this.userName = suggestion.userName;
				this.userId = suggestion.id;
				this.showSuggestions = false;
				this.fetchData();
			},
			// 请求姓名建议列表
			async fetchUserNameSuggestions(name) {
				console.log('开始获取建议, 输入值:', name);
				if (!name || name.trim() === '') {
					this.suggestions = [];
					this.showSuggestions = false;
					return;
				}
				//代码优化
				getUserList(name, 10)
					.then((response) => {
						console.log('姓名建议:', response);
						if (response && Array.isArray(response)) {
							this.suggestions = response;
							this.showSuggestions = this.suggestions.length > 0;
						} else {
							this.suggestions = [];
							this.showSuggestions = false;
						}
					})
					.catch((error) => {
						console.error('获取姓名建议失败:', error);
						this.suggestions = [];
						this.showSuggestions = false;
					});


			},
			onSearchInput(e) {
				console.log('搜索输入:', e);
				// 清除之前的定时器
				if (this.debounceTimer) {
					clearTimeout(this.debounceTimer);
				}

				// 设置新的定时器，实现防抖
				this.debounceTimer = setTimeout(() => {
					const value = e; // 直接使用传入的值而不是this.userName
					if (value && value.length > 0) {
						this.fetchUserNameSuggestions(value);
					} else {
						this.suggestions = [];
						this.showSuggestions = false;
					}
				}, 300); // 300ms的防抖延迟
			},
			loadMore() {
				console.log("loadMore triggered");
				if (this.loadingStatus === "noMore" || this.loadingStatus === "loading") {
					return;
				}
				this.loadingStatus = "loading";
				this.page++;
				this.fetchData();
			},
			// 请求后端 API 获取数据
			async fetchData() {
				try {
					let id = ''
					await this.$store.dispatch('GetInfo').then((res) => {
						console.log('获取用户信息结果', res.id);

						id = res.id
					});

					if (this.userId) {
						console.log('获取用户信息结果', this.userId);
						id = this.userId;
					}
					console.log('id:' + id);
					this.loading = true;
					const response = await request({
						url: '/ApiCaScore/GetByUserId',
						method: 'POST',
						data: {
							userId: id
						},
					});
					console.log("response", response);

					if (response && response.length > 0) {
						// 当页码为1时替换数据，否则追加数据

						this.dataList = response;


						// 设置加载状态
						// this.loadingStatus = newData.length < this.pageSize ? "noMore" : "more";
					} else {
						// 如果没有数据，设置为没有更多
						this.loadingStatus = "noMore";
						if (this.page === 1) {
							this.dataList = []; // 清空数据
						}
						uni.showToast({
							title: '没有更多数据了',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('请求失败:', error);
					this.loadingStatus = "more"; // 出错时重置状态
				} finally {
					this.loading = false;
				}
			},
			// 跳转到详情页面
			goToDetail(id) {
				uni.navigateTo({
					url: `/pages/transcript/score/detail?id=${id}`,
				});
			},
			onSearchConfirm(res) {
				console.log(res.value);
				this.dataList = [];
				this.page = 1;
				this.userName = res.value;
				this.userNamekey = res.value;
				this.loadingStatus = "more"; // 重置加载状态
				console.log('执行搜索:', res.value);
				this.fetchData();
			},
			cancel() {
				this.dataList = [];
				this.page = 1;
				this.userName = '';
				this.userNamekey = '';
				this.loadingStatus = "more"; // 重置加载状态
				this.fetchData();
			}
		},
		// 监听页面滚动
		onPageScroll(e) {
			return
			console.log('触底')
			this.fetchData(); // 加载更多数据
			return
			if (this.isScrollLoading || this.loading || this.noMore) return; // 防止重复加载

			// 获取页面高度
			const query = uni.createSelectorQuery().in(this);
			query.select('.table-page').boundingClientRect((res) => {
				if (res && res.height) {
					const windowHeight = uni.getSystemInfoSync().windowHeight; // 窗口高度
					const scrollBottom = e.scrollTop + windowHeight; // 滚动到底部的位置

					// 判断是否滚动到底部
					if (scrollBottom >= res.height - 50) {
						this.isScrollLoading = true; // 标记为正在滚动加载

					}
				}
			}).exec();
		}
	};
</script>

<style scoped>
	.ab-tr{
		right: 10px;
		top: 10px;
		font-weight: bold;
		
	}
	.scroll-box {
		height: calc(100vh - 50px);
		/* 减去搜索栏的高度 */
		overflow-y: auto;
	}

	.container {
		display: flex;
		flex-direction: column;
		height: 100vh;
	}

	/* 头部区域 */
	.header {
		padding: 20px;
		background-color: #007AFF;
		color: #fff;
		text-align: center;
	}

	.username {
		font-size: 18px;
		font-weight: bold;
	}

	/* 表格页面 */
	.table-page {
		flex: 1;
		margin: 10px 0;
		overflow-y: auto;
		/* 允许滚动 */
	}

	.table {
		width: 100%;
		border-collapse: collapse;
	}

	.table-row {
		display: flex;
		border-bottom: 1px solid #ddd;
		padding: 5px 0;
		font-weight: 600;
	}

	.table-header {
		font-weight: bold;
		background-color: #f5f5f5;
	}

	.table-cell {
		flex: 1;
		padding: 10px;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.ranking-cell {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.ranking-up {
		color: #E06C75;
		margin-left: 4px;
		font-weight: bold;
	}

	.ranking-down {
		color: #77C2FA;
		margin-left: 4px;
		font-weight: bold;
	}

	.loading,
	.no-more {
		text-align: center;
		padding: 10px;
		color: #888;
	}

	/* 姓名建议列表样式 */
	.suggestion-container {
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 14px;
		margin: 0 15px;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
		max-height: 200px;
		overflow-y: auto;
		z-index: 100;
		position: relative;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.8);
		transform: translateY(5px);
		animation: fadeIn 0.3s ease-out forwards;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10px);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.suggestion-item {
		padding: 14px 18px;
		border-bottom: 1px solid rgba(240, 240, 240, 0.8);
		font-size: 14px;
		transition: all 0.25s ease;
		position: relative;
		overflow: hidden;
	}

	.suggestion-item:last-child {
		border-bottom: none;
	}

	.suggestion-item:active {
		background-color: rgba(245, 247, 250, 0.8);
	}

	.suggestion-item::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 0;
		background: linear-gradient(90deg, #6a89cc, #4a69bd);
		z-index: -1;
		transition: height 0.25s ease;
		opacity: 0;
	}

	.suggestion-item:hover {
		color: #4a69bd;
		padding-left: 24px;
	}

	.suggestion-item:hover::after {
		height: 3px;
		opacity: 1;
	}
</style>