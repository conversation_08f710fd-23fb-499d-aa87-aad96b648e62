import store from '@/store'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value) {
	if (value && value instanceof Array && value.length > 0) {
		const permissions = store.getters && store.getters.permissions
		const permissionDatas = value
		const all_permission = "*:*:*"

		const hasPermission = permissions.some(permission => {
			return all_permission === permission || permissionDatas.includes(permission)
		})

		if (!hasPermission) {
			return false
		}
		return true
	} else {
		console.error(`需要权限ID！比如checkPermi([100，101])`)
		return false
	}
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {

	if (value && value instanceof Array && value.length > 0) {
		const roles = store.getters && store.getters.roles
		const permissionRoles = value
		const super_admin = "管理员"

		if (super_admin === roles || permissionRoles.includes(roles)) return true
		
		return false

	} else {
		console.error(`需要角色！比如checkRole(['管理员'，'教师'])`)
		return false
	}
}