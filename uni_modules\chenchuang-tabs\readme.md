# chenchuang-tabs 前端仿今日头条 tabs 选项卡tabs标签页，根据文字多少自适应 tab项宽度示例


##uniapp专属精品组件页面模板（由前端组件开发出品）精品组件页面模板

###●组件模板规划：
由前端组件开发出品的精品组件页面模板，将陆续发布，预计高达约几百种供您使用，是快速快发项目、创业的必备精品。

合集地址： uni-app模板合集地址：(https://ext.dcloud.net.cn/publisher?id=274945) 如查看全部页面模板，请前往上述uniapp插件市场合集地址；

###●组件模板效果图：
可下载项目后预览，效果图见右侧图片；

###●组件模板费用：
学习：免费下载，进行学习，无费用；

使用/商用：本页面地址赞赏10元后，可终身商用；

###●组件模板使用版权/商用：
本组件模板免费下载可供学习，如需使用及商用，请在本组件页面模板进行赞赏10元

（仅需10元获取精品页面模板代码-物有所值，1个组件页面市场价100元 ）

赞赏10元后（当前项目产生赞赏订单可追溯）即可终身商用当前本地址下载的页面模版代码，不同下载地址需进行不同的赞赏。（不赞赏就进行商用使用者，面临侵权！保留追究知识产权法律责任！后果自负！）

### 我的技术公众号(私信可加前端技术交流群)

群内气氛挺不错的，应该或许可能大概，算是为数不多的，专搞技术的前端群，偶尔聊天摸鱼

![图片](https://i.postimg.cc/RZ0sjnYP/front-End-Component.jpg)


#### 使用方法 
```使用方法
	
<!-- spaceLeft设置tabs间距 v-model：绑定选择序列 tabs: 选择数据 change：切换事件  -->
<chenchuang-tabs spaceLeft="12" v-model="industryTabIndex" :tabs="industryTabs" @change="tabChange"></chenchuang-tabs>
					
				
```

#### HTML代码实现部分
```html

<template>
	<view class="content">

		<!-- chenchuang-tabs组件，根据文字自适应tab项宽度，支持自定义标题栏 -->
		<view style="margin-top:14px; margin-left: 8px; margin-right: 10px;">
			<!-- spaceLeft设置tabs间距 v-model：绑定选择序列 tabs: 选择数据 change：切换事件  -->
			<chenchuang-tabs spaceLeft="12" v-model="industryTabIndex" :tabs="industryTabs"
				@change="tabChange"></chenchuang-tabs>

			<!-- 列表组件 -->
			<CCBProjectList :productList="projectList" @click="goProDetail"></CCBProjectList>
		</view>
	</view>
</template>

<script>
	import CCBProjectList from '@/components/CCProjectList.vue';

	export default {
		components: {

			CCBProjectList

		},
		data() {
			return {
				// 列表数组
				projectList: [],
				industryTabs: [{
						name: '光伏产业'
					},
					{
						name: '新能源车电池'
					},
					{
						name: '食品饮料白酒'
					},
					{
						name: '医疗健康'
					},
					{
						name: '银行金融'
					},
					{
						name: '食品饮料白酒'
					},
					{
						name: '行业七'
					},
					{
						name: '行业八'
					}
				],
				industryTabIndex: 0,

			}
		},
		mounted() {
			this.requestData();
		},
		methods: {

			tabChange() {
				console.log('切换行业类型 =' + this.industryTabIndex);
			},
			requestData() {

				// 模拟请求参数设置
				let reqData = {

					'area': '',
					"pageSize": 10,
					"pageNo": this.curPageNum
				}
				// 模拟请求接口
				this.totalNum = 39;
				this.projectList = [];
				for (let i = 0; i < 10; i++) {

					this.projectList.push({
						'proName': '项目名称' + i,
						'proUnit': '公司名称' + i,
						'area': '广州',
						'proType': '省级项目',
						'stage': '已开工',
						'id': i + ''
					});
				}
			}
		}
	}
</script>

<style>
	page {

		background-color: #f6f6f6;
	}

	.content {
		display: flex;
		flex-direction: column;


	}
</style>


```
