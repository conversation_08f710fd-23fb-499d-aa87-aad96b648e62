<template>
  <view
    class="switch-block"
    :style="{
      'width': w + 'rpx',
      'height': h + 'rpx'
    }"
    @tap="touchSwitchHandle">
    <!-- 滑动的block -->
    <view
      class="switch-block_move"
      :style="{
        'left': ((w / switchList.length) * value - 2) + 'rpx',
        'width': (w / switchList.length) + 'rpx',
        'height': (h - 4) + 'rpx'
      }">
    </view>

    <!-- 展示block中数据 -->
    <view
      class="switch-block_item"
      :class="{'switch-checked': value === idx}"
      v-for="(val, idx) in switchList"
      :key="idx"
      :style="{
        'left': ((w / switchList.length) * idx) + 'rpx',
        'width': (w / switchList.length) + 'rpx',
        'height': (h - 4) + 'rpx',
        'line-height': (h - 4) + 'rpx'
      }">
      <text
        class="text"
        @tap="switchItemHandle(val, idx, $event)"
      >{{val}}</text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 数据格式为Array<String>
    switchList: {
      type: Array,
      required: true
    },
    // 当前选中的索引
    value: {
      type: Number,
      default: 1
    },
    // rpx
    width: {
      type: [Number, String],
      required: true
    },
    // rpx
    height: {
      type: [Number, String],
      required: true
    }
  },
  computed: {
    /**
     * 是否阻止block的事件
     * block = 2，点击整体组件即可进行滑块切换，目的是类似常见switch开关组件，点击即切换
     * block > 2，则阻止整体组件的点击滑动，目的是不清楚整体点击的索引，所以只能点击item块进行切换
     * block < 2，则不做操作
     */
    isPreventBlockEvent() {
      return (this.switchList.length <= 2)
    },
    w() {
      return Number(this.width)
    },
    h() {
      return Number(this.height)
    }
  },
  methods: {
    // block = 2，点击switch整体时，两个block左右切换
    touchSwitchHandle() {
      let len = this.switchList.length
      if (len > 2) return
      if (len < 2) return
      this.$emit('input', this.value ? 0 : 1)
      let index = this.value
      let item = this.switchList[index]
      this.$emit('change', {
        index,
        item
      })
    },
    // block > 2，点击item块，进行切换
    switchItemHandle(item, index, e) {
      if (this.isPreventBlockEvent) {
        return // 阻止block点击
      } else {
        try { // 阻止冒泡
          let stop = e ? e.stopPropagation : null
          stop && stop()
          let def = e ? e.defaultPrevented : null
          def && def()
        } catch (error) {}
      }

      if (index === this.value) return
      this.$emit('input', index)
      this.$emit('change', {
        index,
        item
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.switch-block {
  background: rgba(220, 239, 251, 0.7);
  border-radius: 32rpx;
  position: relative;
  margin-right: auto;
  &_move {
    background: #FFFFFF;
    box-shadow: 0 0 12rpx 0 rgba(0, 0, 0, 0.06);
    border-radius: 32rpx;
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    left: 0;
    z-index: 1;
    transition: .3s all;
  }
  &_item {
    position: absolute;
    top: 0;
    bottom: 0;
    margin: auto;
    font-size: 30rpx;
    font-weight: 600;
    color: #f8f7f6;
    text-align: center;
    z-index: 2;
    transition: .3s all;
    .text {
      width: 100%;
      height: 100%;
      display: inline-block;
      text-align: center;
      line-height: 1;
    }
  }
  .switch-checked {
    color: #0D59D2;
  }
}
</style>