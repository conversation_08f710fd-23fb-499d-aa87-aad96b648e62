### wsx-tab 选项卡 折叠展开 滑动 切换 选中居中定位

> **组件名：wsx-tab**
> 关联组件：`scroll-view`、`tab`

支持折叠展开 滑动，选项卡，切换，选中居中定位，回选等，支持微信小程序，H5，app等多端组件。

### 属性说明：

|属性名		|类型	|默认值	|说明					|
|---		|----	|---	|---					|
|value		|String	|""		|v-model绑定值			|
|options	|Array	|-		|选项数据，格式：[{},{}]	|
|valueKey	|String	|value	|取值的对象键名			|
|labelKey	|String	|label	|显示的对象键名			|
|isCollapse	|Boolean|false	|是否展开所有				|

### 事件说明：

|事件称名	|说明							|
|---	|----							|
|change	|点击确定后执行的事件返回对象和字符串	|

### 使用方式：

在 ``script`` 中引用组件 

```javascript
	import wsxTab from "@/uni_modules/wsx-tab/wsx-tab.vue"
export default {
	components: {
		wsxTab
	},
	data() {
		return {
			title: 'Hello',
			value: 12,
			list: [{
				value: 1,
				label: '苹果'
			}, {
				value: 2,
				label: '香蕉'
			}, {
				value: 3,
				label: '葡萄'
			}, {
				value: 4,
				label: '榴莲'
			}, {
				value: 5,
				label: '柚子'
			}, {
				value: 6,
				label: '桃子'
			}, {
				value: 7,
				label: '芒果'
			}, {
				value: 8,
				label: '樱桃'
			}, {
				value: 9,
				label: '脆枣'
			}, {
				value: 10,
				label: '柿子'
			}, {
				value: 11,
				label: '柠檬'
			}, {
				value: 12,
				label: '橘子'
			}, {
				value: 13,
				label: '黄瓜'
			}, {
				value: 14,
				label: '软枣'
			}, {
				value: 15,
				label: '梨'
			}]
		}
	},

	methods: {
		change(e1, e2) {
			console.log(e1, e2)
		},
	}
}
```

在 ``template`` 中使用组件

```html
<wsx-tab v-model="value" :options="list" @change="change"></wsx-tab>
```