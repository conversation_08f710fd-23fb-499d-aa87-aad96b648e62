{"name": "社科赛斯学管系统", "appid": "__UNI__C00AEA1", "description": "为考生提供相应服务", "versionName": "1.2.3", "versionCode": 123, "transformPx": false, "networkTimeout": {"request": 60000}, "sassImplementationName": "node-sass", "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "modules": {"VideoPlayer": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"], "intentFilters": [{"action": "android.intent.action.VIEW", "data": {"scheme": "file", "mimeType": "application/pdf"}, "category": ["android.intent.category.DEFAULT", "android.intent.category.BROWSABLE"]}]}, "ios": {"dSYMs": false, "UIFileSharingEnabled": true, "UTExportedTypeDeclarations": [{"UTTypeIdentifier": "com.adobe.pdf", "UTTypeDescription": "PDF Document", "UTTypeConformsTo": ["public.data"], "UTTypeTagSpecification": {"public.filename-extension": ["pdf"]}}]}, "sdkConfigs": {"geolocation": {"system": {"__platform__": ["ios", "android"]}}, "ad": {}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx1da2bf060b4bc7b0", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true}, "vueVersion": "2", "h5": {"template": "static/index.html", "devServer": {"port": 9090, "https": false}, "title": "RuoYi-App", "router": {"mode": "hash", "base": "./"}}, "locale": "zh-Hans"}