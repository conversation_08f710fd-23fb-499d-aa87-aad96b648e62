<template>
	<view class="wsx-tab">
		<scroll-view v-show="!isCollapseShow" class="tab-scroll-view" scroll-x="true" scroll-with-animation="true"
			:scroll-left="slidePosition">
			<view v-for="(item, index) in options" :key="index" class="scroll-view-item"
				:class="'position-item'+item[valueKey]" @click="selectChange(item)">
				<view class="itme-tab" :class="activeId==item[valueKey] ? 'active-tab' : 'default-tab'">
					{{item[labelKey]}}
				</view>
			</view>
			<view v-if="isCollapse" class="collapse"></view>
		</scroll-view>
		<view v-if="isCollapse" v-show="!isCollapseShow" class="collapse-list" @click="collapseChange">
			<image class="tab-img" src="~@/static/images/more.png"></image>
		</view>
		<view v-show="isCollapseShow">
			<view class="collapse-row">
				<view class="collapse-title">
					<image class="tab-img" src="~@/static/images/all.png"></image>
				</view>
				<view class="collapse-list" @click="collapseChange">
					<image class="tab-img" src="~@/static/images/more.png"></image>
				</view>
			</view>
			<view class="collapse-content">
				<view v-for="(item, index) in options" :key="index"
					:class="activeId==item[valueKey] ? 'active-tab' : 'default-tab'" class="collapse-content-item"
					@click="selectChange(item)">{{item[labelKey]}}</view>
			</view>
		</view>
	</view>
</template>
<script>
	/**
	 * wsx-tab组件  
	 * @property {String} value v-model绑定值
	 * @property {Array} options 选项数据，格式：[{},{}]
	 * @property {String} labelKey 显示的对象键名 
	 * @property {String} valueKey 取值的对象键名
	 * @property {Boolean} isCollapse 是否展开所有
	 */
	export default {
		name: 'wsxTab',
		props: {
			value: null,
			options: {
				type: Array,
				required: true,
				default: [],
			},
			labelKey: {
				type: String,
				default: "label"
			},
			valueKey: {
				type: String,
				default: "value"
			},
			isCollapse: {
				type: Boolean,
				default: true
			},
		},
		data() {
			return {
				globalConfig: getApp().globalData.config,
				scrollWidth: 0,
				slidePosition: 0, // 滑动位置
				isCollapseShow: false,
				activeId: this.value,
			}
		},
		mounted() {
			this.initCalculateLocation(this.value)
		},
		methods: {
			// 初始化元素定位
			initCalculateLocation(id) {
				this.activeId = id
				let index = 0

				const query = uni.createSelectorQuery().in(this)
				// 取得 scroll-view 组件宽度
				query.select('.tab-scroll-view').boundingClientRect(data => {
					this.scrollWidth = data.width
				}).exec();
				query.selectAll('.scroll-view-item').boundingClientRect()
				query.exec((res) => {
					for (var i = 0; i < this.options.length; i++) {
						if (this.options[i][this.valueKey] == id) {
							index = i
						}
						this.options[i].left = res[1][i].left
						this.options[i].width = res[1][i].width
					}
					this.slidePosition = this.options[index].left - this.scrollWidth / 2 + this.options[index]
						.width / 2;
				})
			},
			// 计算scroll-view 滚动位置
			calculateLocation(id) {
				this.activeId = id
				let index = 0
				for (var i = 0; i < this.options.length; i++) {
					if (this.options[i][this.valueKey] == id) {
						index = i
					}
				}
				this.slidePosition = this.options[index].left - this.scrollWidth / 2 + this.options[index]
					.width / 2;
			},
			//点击展开选项
			selectCollapseChange(item) {
				this.isCollapseShow = false
				this.calculateLocation(item[this.valueKey])
				this.$emit("input", item[this.valueKey]);
				this.$emit("change", item, item[this.labelKey]);
			},
			// 点击选项
			selectChange(item) {
				this.calculateLocation(item[this.valueKey])
				this.$emit("input", item[this.valueKey]);
				this.$emit("change", item.path, item[this.labelKey]);
			},
			// 展开折叠
			collapseChange() {
				this.isCollapseShow = !this.isCollapseShow
			}
		},
	}
</script>

<style scoped>
	/* 隐藏滚动条，但依旧具备可以滚动的功能 */
	::v-deep .uni-scroll-view::-webkit-scrollbar {
		display: none;
	}

	.wsx-tab {
		position: relative;
		width: 100%;
	}

	.tab-scroll-view {
		position: relative;
		padding: 10rpx 0 10rpx 0;
		width: 100%;
		overflow: hidden;
		white-space: nowrap;
		border-radius: 12upx;
		width: calc(100%-64rpx);
		background-color: #F7F8FA;
	}

	.tab-scroll-view ::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}

	.tab-img {
		width: 35rpx;
		height: 35rpx;
	}

	.scroll-view-item {
		padding: 0 6rpx;
		display: inline-block;
		text-align: center;
		line-height: 60rpx;
		height: 60rpx;
		font-size: 28rpx;
		border-radius: 8rpx;
	}

	.itme-tab {
		padding-left: 12rpx;
		padding-right: 12rpx;
		border-radius: 8rpx;
	}

	.active-tab {
		background-color: #bf2827;
		color: #ffffff;
	}

	.default-tab {
		background-color: #ffffff;
		color: #bf2827;
	}

	.collapse {
		width: 80rpx;
		display: inline-block;
	}

	.collapse-list {
		position: absolute;
		right: 0;
		top: 0;
		width: 80rpx;
		height: 60rpx;
		text-align: center;
		display: grid;
		place-items: center;
		color: #999999;
		padding: 5px 0 5px 0;
		background: #F7F8FA;
	}

	.collapse-row {
		justify-content: space-between;
		border-bottom: 1rpx solid #ebebec;
		background-color: #F7F8FA;
	}

	.collapse-title {
		width: 80rpx;
		height: 60rpx;
		padding: 5px 0 5px 0;
		text-align: center;
		display: grid;
		place-items: center;
	}

	.collapse-content {
		display: flex;
		flex-wrap: wrap;
		padding-left: 24rpx;
		padding-top: 24rpx;
		background-color: #F7F8FA;
	}

	.collapse-content-item {
		padding: 0 15rpx;
		text-align: center;
		line-height: 60rpx;
		font-size: 28rpx;
		border-radius: 8rpx;
		margin: 0rpx 24rpx 24rpx 0rpx;
	}
</style>