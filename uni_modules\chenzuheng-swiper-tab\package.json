{"id": "chenzuheng-swiper-tab", "displayName": "uniapp实现tab选项卡滑动切换功能", "version": "1.0.1", "description": "uniapp实现tab选项卡滑动切换交互功能，支持点击tab选项带动滑动页面，滑动切换tab选项", "keywords": ["tab选项卡", "swiper", "swiper-tabs", "页面滑动", "tab切换"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "uniapp-template-page", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y"}, "快应用": {"华为": "y", "联盟": "y"}}}}}