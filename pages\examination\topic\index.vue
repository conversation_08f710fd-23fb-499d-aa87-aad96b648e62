<template>
	<view class="container" style="height: 100% !important;">
		<view class="swiper-box">
			<swiper class="swiper" :duration="duration" :current="swiperIndex" :disable-programmatic-animation="true"
				@change="onChange" @animationfinish="onAnimationfinish">
				<swiper-item v-for="(item,index) in swiperList" :key="index">
					<scroll-view scroll-y="true" class="swiper-scroll">
						<view class="swiper-item">
							<view class="topic-content">
								<!-- 加载状态显示 -->
								<view v-if="item && item.loading" class="loading-container">
									<text class="loading-text">题目加载中...</text>
								</view>
								<!-- 正常题目内容 -->
								<view v-else-if="item && item.loaded">
									<text style="padding-right: 20rpx;">
										<text class="type-label">
											{{item.subjectType}}
										</text>
									</text>
									<text>{{item.typeFullName}}</text>
								</view>
								<!-- 未加载状态 -->
								<view v-else-if="item">
									<text style="padding-right: 20rpx;">
										<text class="type-label">
											{{item.subjectType}}
										</text>
									</text>
									<text>{{item.typeFullName}}</text>
								</view>
								<image v-if="item && item.loaded && item.subjectFile" :src="item.subjectFile.src" mode="widthFix"
									style="width:100%;margin-top:20rpx;"></image>
								<view class="answer-sheet" v-if="item && item.loaded && item.answerSheet.length > 0">
									<view class="item h-flex-x" v-for="(sheetItem,sheetIndex) in item.answerSheet"
										:key="sheetIndex" @tap="onAnswerSheet(sheetIndex)">
										<view class="icon h-flex-x h-flex-center" :class="{
												'success':(item.answerResult == sheetItem.value) && (item.answer == sheetItem.value),
												'error':(item.answerResult == sheetItem.value) && (item.answer != sheetItem.value)
											}">
											<block v-if="item.answerResult == sheetItem.value">
												<uni-icons type="checkmarkempty" size="12" color="#fff"
													v-if="item.answer == sheetItem.value"></uni-icons>
												<uni-icons type="closeempty" size="12" color="#fff" v-else></uni-icons>
											</block>
											<text v-else>{{sheetItem.value}}</text>
										</view>
										<view>{{sheetItem.name}}</view>
									</view>
								</view>
								<view class="answer-result" v-if="item && item.loaded && item.answerResult">
									<text>答案</text>
									<text
										style="color: #0089ff;font-weight: bold;padding: 0 20rpx;">{{item.answer}}</text>
									<text style="padding-right: 20rpx;">您选择</text>
									<text style="color: #0089ff;font-weight: bold;"
										v-if="item.answerResult == item.answer">{{item.answerResult}}</text>
									<text style="color: #f84d27;font-weight: bold;" v-else>{{item.answerResult}}</text>
								</view>
								<view class="answer-doubt" v-if="item && item.loaded && item.answerResult">
									<text style="font-weight: bold;">题目解析：</text>
									<image v-if="item.analysisFile" :src="item.analysisFile.src" mode="widthFix"
										style="width:100%;margin-top:20rpx;"></image>
								</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
		<view class="panel-bottom">
			<view class="shade" v-if="showPopup" @tap="showPopup = !showPopup"></view>
			<view class="content">
				<view class="action-bar h-flex-x">
					<view class="h-flex-item-grow h-flex-x" @tap="showPopup = !showPopup">
						<view>
							<uni-icons type="checkbox-filled" size="18" color="#0089ff"></uni-icons>
						</view>
						<view class="success-num" style="padding-left: 10rpx;margin-right: 30rpx;">
							{{countResult.success}}
						</view>
						<view>
							<uni-icons type="clear" size="18" color="#f84d27"></uni-icons>
						</view>
						<view class="error-num" style="padding-left: 10rpx;">{{countResult.error}}</view>
					</view>
					<view class="h-flex-x" @tap="showPopup = !showPopup">
						<uni-icons type="bars" size="18" color="#666"></uni-icons>
						<text style="padding-left: 10rpx;font-weight: bold;">{{topicIndex+1}}</text>
						<text style="color: #999;padding: 0 5rpx;">/</text>
						<text style="color: #999;">{{dataList.length}}</text>
					</view>
				</view>

				<scroll-view class="topic-list" v-if="showPopup" style="height: 700rpx;" scroll-y="">
					<view class="list-box h-flex-x h-flex-wrap h-flex-6">
						<view class="list-item" v-for="(item,index) in dataList" :key="index" @tap="pickerTopic(index)">
							<view class="h-flex-x h-flex-center" :class="{
									'active':index == topicIndex,
									'success':item.answerResult && (item.answer == item.answerResult),
									'error':item.answerResult && (item.answer != item.answerResult)
								}">{{index+1}}</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniIcons from '@/components/uni-icons/uni-icons';
	import request from '@/utils/request.js';

	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				exercisesType: '', // 练习类型
				rId: '', // 资源ID
				logId: null, // 做题记录ID
				subjectIds: [], // 考题ID数组
				currentSubjectIndex: 0, // 当前考题索引
				dataList: [], // 数据源
				loadedSubjects: new Map(), // 已加载的考题缓存 Map<id, subjectData>
				loadingSubjects: new Set(), // 正在加载的考题ID集合
				swiperList: [], // 轮播图数据列表
				swiperIndex: 0, // 轮播图当前位置
				isChange: false, // 是否切换
				topicIndex: 0, // 题目位置
				duration: 200, // 动画过渡时长
				showPopup: false, //弹窗是否显示
				userId: null, // 用户ID
				userType: '', // 用户类型
				answerQueue: [], // 答题请求队列
				isSubmitting: false // 标记是否正在提交
			}
		},
		computed: {
			// 结果统计
			countResult() {
				let [success, error] = [0, 0];
				this.dataList.forEach((item) => {
					if (item && item.answerResult) {
						if (item.answerResult == item.answer) {
							success++;
						} else {
							error++;
						}
					}
				})
				return {
					success,
					error
				}
			}
		},
		async onLoad(options) {
			// 获取路由参数
			this.exercisesType = options.exercisesType || '';
			this.rId = options.rId || '';

			// 获取用户信息
			await this.$store.dispatch('GetInfo').then((res) => {
				this.userId = res.id;
				this.userType = res.userType;
			});

			// 开始获取数据流程
			await this.initExerciseData();
		},
		methods: {
			// 初始化练习数据
			async initExerciseData() {
				try {
					// 1. 调用 AddLog 接口创建做题记录
					const logResponse = await this.createExerciseLog();
					if (!logResponse) return;

					// 2. 根据 logId 获取考题ID数组
					const subjectIds = await this.getSubjectIdsByLogId(logResponse.id);
					if (!subjectIds || subjectIds.length === 0) return;

					// 3. 初始化数据列表占位符
					this.initDataListPlaceholders(subjectIds);

					// 4. 预加载前3题
					await this.preloadSubjects([0, 1, 2]);

					// 5. 渲染第一题
					this.renderSwiper(0);
				} catch (error) {
					console.error('初始化练习数据失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'error'
					});
				}
			},

			// 创建做题记录
			async createExerciseLog() {
				try {
					const response = await request({
						url: `/ApiCaSubject/AddLog`,
						method: 'POST',
						data: {
							exercisesType:this.exercisesType,
							rId:this.rId
						}
					});

					if (response) {
						this.logId = response.id;
						return response
					} else {
						uni.showToast({
							title: '加载失败',
							icon: 'error'
						});
						return null;
					}
				} catch (error) {
					console.error('创建做题记录失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'error'
					});
					return null;
				}
			},

			// 根据logId获取考题ID数组
			async getSubjectIdsByLogId(logId) {
				try {
					const response = await request({
						url: '/ApiCaSubject/GetByLogId',
						method: 'POST',
						data: {
							logId: logId
						}
					});

					if (response) {
						this.subjectIds = response;
						return response
					} else {
						uni.showToast({
							title: '获取考题列表失败',
							icon: 'error'
						});
						return null;
					}
				} catch (error) {
					console.error('获取考题ID数组失败:', error);
					uni.showToast({
						title: '获取考题列表失败',
						icon: 'error'
					});
					return null;
				}
			},

			// 初始化数据列表占位符
			initDataListPlaceholders(subjectIds) {
				this.dataList = subjectIds.map((id, index) => ({
					id: id,
					index: index,
					loading: false,
					loaded: false,
					subjectType: '加载中...',
					typeFullName: '',
					answer: '',
					answerSheet: [],
					answerResult: '',
					subjectFile: null,
					analysisFile: null
				}));
			},

			// 预加载指定索引的考题
			async preloadSubjects(indices) {
				const loadPromises = indices
					.filter(index => index >= 0 && index < this.subjectIds.length)
					.map(index => this.loadSubjectByIndex(index));

				await Promise.allSettled(loadPromises);
			},

			// 根据索引加载考题
			async loadSubjectByIndex(index) {
				if (index < 0 || index >= this.subjectIds.length) return;

				const subjectId = this.subjectIds[index];

				// 检查是否已加载或正在加载
				if (this.loadedSubjects.has(subjectId) || this.loadingSubjects.has(subjectId)) {
					return;
				}

				// 标记为正在加载
				this.loadingSubjects.add(subjectId);
				this.dataList[index].loading = true;

				try {
					const subjectDetail = await this.getSubjectById(subjectId);
					if (subjectDetail) {
						// 缓存数据
						this.loadedSubjects.set(subjectId, subjectDetail);

						// 转换并更新数据
						const transformedData = this.transformSingleSubjectData(subjectDetail);
						this.$set(this.dataList, index, {
							...this.dataList[index],
							...transformedData,
							loading: false,
							loaded: true
						});
					}
				} catch (error) {
					console.error(`加载考题${subjectId}失败:`, error);
					this.dataList[index].loading = false;
				} finally {
					this.loadingSubjects.delete(subjectId);
				}
			},

			// 根据ID获取单个考题详情
			async getSubjectById(id) {
				try {
					const response = await request({
						url: '/ApiCaSubject/GetById',
						method: 'POST',
						data: {
							id: id
						}
					});

					if (response) {
						return response
					} else {
						console.error(`获取考题${id}失败`);
						return null;
					}
				} catch (error) {
					console.error(`获取考题${id}详情失败:`, error);
					return null;
				}
			},

			// 转换单个考题数据格式
			transformSingleSubjectData(item) {
				// 处理选项
				let answerSheet = [];
				if (item.option) {
					// 分割选项字符串
					const options = item.option.split(',');
					answerSheet = options.map((opt, index) => ({
						value: String.fromCharCode(65 + index), // A, B, C, D...
						name: opt.trim()
					}));
				}

				return {
					id: item.id,
					subjectType: item.subjectType || '题目',
					typeFullName: item.typeFullName || '',
					answer: item.answer || '',
					answerSheet: answerSheet,
					answerResult: '', // 初始未答题
					subjectFile: item.subjectFile, // 题目图片信息
					analysisFile: item.analysisFile // 解析图片信息
				};
			},

			// 渲染 swiper
			renderSwiper(index) {
				// 确保索引在有效范围内
				if (index < 0) index = 0;
				if (index >= this.dataList.length) index = this.dataList.length - 1;

				let list = [];
				let current = 1;

				// 添加前一题（如果存在）
				if (index > 0 && this.dataList[index - 1]) {
					list.push(this.dataList[index - 1]);
				} else {
					current = 0;
				}

				// 添加当前题
				if (this.dataList[index]) {
					list.push(this.dataList[index]);
				}

				// 添加后一题（如果存在）
				if (index < this.dataList.length - 1 && this.dataList[index + 1]) {
					list.push(this.dataList[index + 1]);
				}

				this.duration = 0;

				setTimeout(() => {
					this.swiperList = list;
					this.swiperIndex = current;

					setTimeout(() => {
						this.duration = 200;
					}, 100)
				}, 50)
			},

			// 轮播图切换
			onChange(e) {
				// 非触摸事件不做轮播图状态更新
				if (e.detail.source != "touch") return;

				// 标识已切换
				this.isChange = true;

				// 计算新索引
				let newIndex = this.topicIndex;
				if (e.detail.current > this.swiperIndex) {
					newIndex++;
				} else {
					newIndex--;
				}

				// 边界检查
				if (newIndex < 0) newIndex = 0;
				if (newIndex >= this.dataList.length) newIndex = this.dataList.length - 1;

				this.topicIndex = newIndex;
				this.swiperIndex = e.detail.current;

				// 触发懒加载
				this.triggerLazyLoad(newIndex);
			},

			// 轮播图动画结束
			onAnimationfinish(e) {
				if (!this.isChange) return;

				this.isChange = false;
				setTimeout(() => {
					this.renderSwiper(this.topicIndex);
				}, 10);
			},

			// 选择题目
			pickerTopic(index) {
				// 边界检查
				if (index < 0) index = 0;
				if (index >= this.dataList.length) index = this.dataList.length - 1;

				this.topicIndex = index;
				this.renderSwiper(index);
				this.showPopup = false;

				// 触发懒加载
				this.triggerLazyLoad(index);
			},

			// 触发懒加载
			async triggerLazyLoad(currentIndex) {
				// 预加载当前题目及前后各2题
				const indicesToLoad = [];
				for (let i = currentIndex - 2; i <= currentIndex + 2; i++) {
					if (i >= 0 && i < this.dataList.length && !this.dataList[i].loaded && !this.dataList[i].loading) {
						indicesToLoad.push(i);
					}
				}

				if (indicesToLoad.length > 0) {
					await this.preloadSubjects(indicesToLoad);
					// 重新渲染当前swiper以更新数据
					this.renderSwiper(currentIndex);
				}
			},

			// 监听答题
			async onAnswerSheet(index) {
				if (!this.swiperList[this.swiperIndex] || this.swiperList[this.swiperIndex].answerResult) {
					// 不能重复答题
					return;
				}

				// 当前已选答案
				let selectedValue = this.swiperList[this.swiperIndex].answerSheet[index].value;
				// 当前考题ID
				let subjectId = this.swiperList[this.swiperIndex].id;

				// 更新本地答题结果
				this.$set(this.swiperList[this.swiperIndex], "answerResult", selectedValue);

				// 提交用户答案
				// await this.submitAnswer(subjectId, selectedValue);
			},

			// 提交用户答案
			async submitAnswer(id, answer) {
				// 将请求加入队列
				this.answerQueue.push({
					id,
					answer
				});

				// 如果没有正在处理的请求，则开始处理队列
				if (!this.isSubmitting) {
					this.processAnswerQueue();
				}
			},

			// 处理答题队列
			async processAnswerQueue() {
				if (this.answerQueue.length === 0) {
					this.isSubmitting = false;
					return;
				}

				this.isSubmitting = true;
				const {
					id,
					answer
				} = this.answerQueue.shift();

				try {
					// 发送请求 - 这里需要根据实际的提交答案接口调整
					const response = await request({
						url: '/ApiCaSubject/SubmitAnswer', // 假设的提交答案接口
						method: 'POST',
						data: {
							subjectId: id,
							answer: answer,
							logId: this.logId
						},
					});

					// 请求成功后处理下一个
					this.processAnswerQueue();
				} catch (error) {
					console.error('提交答案失败:', error);
					// 失败后重试当前请求
					this.answerQueue.unshift({
						id,
						answer
					});
					setTimeout(() => this.processAnswerQueue(), 1000);
				}
			}
		}
	}
</script>

<style lang="scss">
	@import "@/lib/helang-flex.scss";

	uni-page-body {
		height: 100% !important;
	}

	page {
		height: 100% !important;
		display: inline;
	}

	// 容器
	.container {
		height: 100%;
		background-color: #fff;
		position: relative;
		color: #333;
		font-size: 28rpx;
	}

	// 轮播盒子
	.swiper-box {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 100rpx;
		height: auto;
		width: auto;
		z-index: 1;
	}

	.swiper {
		height: 100%;

		.swiper-scroll {
			height: 100%;
		}

		.swiper-item {
			background-color: #fff;
			box-sizing: border-box;
			height: 100%;

			.topic-content {
				padding: 30rpx;
				font-size: 32rpx;
				color: #333;
				line-height: 1.6;

				// 题目类型标签
				.type-label {
					background-color: #0089ff;
					color: #fff;
					border-radius: 6rpx;
					padding: 4rpx 10rpx;
					font-size: 24rpx;
				}

				// 答题卡
				.answer-sheet {
					margin: 40rpx -30rpx;

					.item {
						padding: 20rpx 30rpx;

						&:active {
							background-color: #f3f3f3;
						}

						+.item {
							margin-top: 0;
						}

						.icon {
							margin-right: 20rpx;
							box-shadow: 0 0 5px #999;
							width: 40rpx;
							height: 40rpx;
							border-radius: 50%;
							font-size: 26rpx;

							&.success {
								background-color: #0089ff;
							}

							&.error {
								background-color: #f84d27;
							}
						}
					}
				}

				// 答题结果
				.answer-result {
					background-color: #f3f3f3;
					border-radius: 8rpx;
					padding: 20rpx 30rpx;
					font-size: 28rpx;
				}

				// 题目解析
				.answer-doubt {
					font-size: 28rpx;
					color: #8a6d3b;
					background-color: #fcf8e3;
					border: #faebcc solid 1px;
					margin-top: 40rpx;
					padding: 20rpx 30rpx;
					border-radius: 8rpx;
					word-break: break-all
				}
			}
		}
	}

	// 底部面板
	.panel-bottom {
		position: absolute;
		left: 0;
		bottom: 0;
		height: auto;
		width: 100%;
		z-index: 2;

		// 遮罩
		.shade {
			position: fixed;
			z-index: 9;
			background-color: rgba(33, 33, 33, 0.5);
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}

		// 内容
		.content {
			position: absolute;
			z-index: 10;
			bottom: 0;
			left: 0;
			width: 100%;
			height: auto;
			background-color: #fff;
		}

		// 操作栏
		.action-bar {
			height: 100rpx;
			width: 100%;
			box-sizing: border-box;
			border-top: #ddd solid 1px;
			font-size: 28rpx;
			padding: 0 20rpx;

			.success-num {
				color: #0089ff;
			}

			.error-num {
				color: #f84d27;
			}
		}

		// 题目列表
		.topic-list {
			height: 600rpx;

			.list-box {
				padding: 20rpx 20rpx 0 20rpx;
				font-size: 28rpx;
				color: #666;

				.list-item {
					padding-bottom: 20rpx;

					>view {
						width: 80rpx;
						height: 80rpx;
						background-color: #fff;
						border: #ddd solid 1px;
						border-radius: 50%;
						margin: 0 auto;

						&.active {
							border: #0089ff solid 1px;
						}

						&.success {
							background-color: #dbf2ff;
							color: #0089ff;
							border: none;

							&.active {
								border: #0089ff solid 1px;
							}
						}

						&.error {
							background-color: #ffeceb;
							color: #f84d27;
							border: none;

							&.active {
								border: #f84d27 solid 1px;
							}
						}
					}
				}
			}
		}
	}
</style>