<template>
	<view class="container" style="height: 100% !important;">
		<view class="swiper-box">
			<swiper class="swiper" :duration="duration" :current="swiperIndex" :disable-programmatic-animation="true"
				@change="onChange" @animationfinish="onAnimationfinish">
				<swiper-item v-for="(item,index) in swiperList" :key="index">
					<scroll-view scroll-y="true" class="swiper-scroll">
						<view class="swiper-item">
							<view class="topic-content">
								<!-- 加载状态显示 -->
								<view v-if="item && item.loading" class="loading-container">
									<text class="loading-text">题目加载中...</text>
								</view>
								<!-- 正常题目内容 -->
								<view v-else-if="item && item.loaded">
									<text style="padding-right: 20rpx;">
										<text class="type-label">
											{{item.subjectType}}
										</text>
									</text>
									<text>{{item.typeFullName}}</text>
								</view>
								<!-- 未加载状态 -->
								<view v-else-if="item">
									<text style="padding-right: 20rpx;">
										<text class="type-label">
											{{item.subjectType}}
										</text>
									</text>
									<text>{{item.typeFullName}}</text>
								</view>
								<image v-if="item && item.loaded && item.subjectFile" :src="item.subjectFile.src"
									mode="widthFix" style="width:100%;margin-top:20rpx;"></image>
								<view class="answer-sheet" v-if="item && item.loaded && item.answerSheet.length > 0">
									<view class="item h-flex-x" v-for="(sheetItem,sheetIndex) in item.answerSheet"
										:key="sheetIndex" @tap="onAnswerSheet(sheetIndex)">
										<!-- 多选题样式 -->
										<view v-if="item.isMultipleChoice" class="icon h-flex-x h-flex-center checkbox"
											:class="{
												'selected': !item.isSubmitted && item.userAnswer && Array.isArray(item.userAnswer) && item.userAnswer.includes(sheetItem.value),
												'success': item.isSubmitted && item.userAnswer  && item.answer && item.answer.split(',').includes(sheetItem.value) && item.userAnswer.includes(sheetItem.value),
												'error': item.isSubmitted && item.userAnswer   && (!item.answer || !item.answer.split(',').includes(sheetItem.value)) && item.userAnswer.includes(sheetItem.value),
												'missed': item.isSubmitted && item.userAnswer   && item.answer && item.answer.split(',').includes(sheetItem.value) && !item.userAnswer.includes(sheetItem.value)
											}">
											<template
												v-if="item.isSubmitted && item.userAnswer && Array.isArray(item.userAnswer) && item.userAnswer.includes(sheetItem.value)">
												<uni-icons type="checkmarkempty" size="12" color="#fff"
													v-if="item.answer && item.answer.split(',').includes(sheetItem.value)"></uni-icons>
												<uni-icons type="closeempty" size="12" color="#fff" v-else></uni-icons>
											</template>
											<text v-else>{{sheetItem.value}}</text>
										</view>
										<!-- 单选题样式 -->
										<view v-else class="icon h-flex-x h-flex-center radio" :class="{
												'selected': !item.isSubmitted && item.userAnswer == sheetItem.value,
												'success': item.isSubmitted && (item.userAnswer == sheetItem.value) && (item.answer == sheetItem.value),
												'error': item.isSubmitted && (item.userAnswer == sheetItem.value) && (item.answer != sheetItem.value)
											}">
											<template v-if="item.isSubmitted && item.userAnswer == sheetItem.value">
												<uni-icons type="checkmarkempty" size="12" color="#fff"
													v-if="item.answer == sheetItem.value"></uni-icons>
												<uni-icons type="closeempty" size="12" color="#fff" v-else></uni-icons>
											</template>
											<text v-else>{{sheetItem.value}}</text>
										</view>
										<view class="option-content">
											<text class="option-text">{{sheetItem.name}}</text>
											<!-- 答案统计显示 -->
											<view v-if="item.isSubmitted && item.answerStatistics && item.totalAnswerCount > 0" class="option-statistics">
												<text class="statistics-count">{{(item.answerStatistics[sheetItem.value] || 0)}}人</text>
												<text class="statistics-percentage">({{item.totalAnswerCount > 0 ? ((item.answerStatistics[sheetItem.value] || 0) / item.totalAnswerCount * 100).toFixed(1) : 0}}%)</text>
											</view>
										</view>
									</view>
									<!-- 提交按钮 -->
									<view class="submit-button-container"
										v-if="!item.isSubmitted && ((item.isMultipleChoice && item.userAnswer && item.userAnswer.length > 0) || (!item.isMultipleChoice && item.userAnswer))">
										<button class="submit-button" @tap="submitCurrentAnswer">提交答案</button>
									</view>
								</view>
								<view class="answer-result" v-if="item && item.loaded && item.isSubmitted">
									<text>答案</text>
									<text
										style="color: #0089ff;font-weight: bold;padding: 0 20rpx;">{{item.answer}}</text>
									<text style="padding-right: 20rpx;">您选择</text>
									<!-- 多选题答案显示 -->
									<text v-if="item.isMultipleChoice" style="font-weight: bold;"
										:style="{color: item.isCorrect ? '#0089ff' : '#f84d27'}">
										{{item.userAnswer}}
									</text>
									<!-- 单选题答案显示 -->
									<text v-else style="font-weight: bold;"
										:style="{color: (item.userAnswer == item.answer) ? '#0089ff' : '#f84d27'}">
										{{item.userAnswer}}
									</text>
								</view>
								<view class="answer-doubt" v-if="item && item.loaded && item.isSubmitted">
									<text style="font-weight: bold;">题目解析：</text>
									<view v-if="!item.analysisFile" style="color: #999; margin-top: 20rpx;">
										暂无解析图片
									</view>
									<image v-if="item.analysisFile && item.analysisFile.src"
										:src="item.analysisFile.src" mode="widthFix"
										style="width:100%;margin-top:20rpx;"></image>
								</view>
							</view>
						</view>
					</scroll-view>
				</swiper-item>
			</swiper>
		</view>
		<view class="panel-bottom">
			<view class="shade" v-if="showPopup" @tap="showPopup = !showPopup"></view>
			<view class="content">
				<view class="action-bar h-flex-x">
					<view class="h-flex-item-grow h-flex-x" @tap="showPopup = !showPopup">
						<view>
							<uni-icons type="checkbox-filled" size="18" color="#0089ff"></uni-icons>
						</view>
						<view class="success-num" style="padding-left: 10rpx;margin-right: 30rpx;">
							{{countResult.success}}
						</view>
						<view>
							<uni-icons type="clear" size="18" color="#f84d27"></uni-icons>
						</view>
						<view class="error-num" style="padding-left: 10rpx;">{{countResult.error}}</view>
					</view>
					<view class="h-flex-x" @tap="showPopup = !showPopup">
						<uni-icons type="bars" size="18" color="#666"></uni-icons>
						<text style="padding-left: 10rpx;font-weight: bold;">{{topicIndex+1}}</text>
						<text style="color: #999;padding: 0 5rpx;">/</text>
						<text style="color: #999;">{{dataList.length}}</text>
					</view>
				</view>

				<scroll-view class="topic-list" v-if="showPopup" style="height: 700rpx;" scroll-y="">
					<view class="list-box h-flex-x h-flex-wrap h-flex-6">
						<view class="list-item" v-for="(item,index) in dataList" :key="index" @tap="pickerTopic(index)">
							<view class="h-flex-x h-flex-center" :class="{
									'active': index === topicIndex,
									'success': item.isSubmitted && item.isCorrect,
									'error': item.isSubmitted && !item.isCorrect,
									'answered': !item.isSubmitted && ((item.isMultipleChoice && Array.isArray(item.userAnswer) && item.userAnswer.length > 0) || (!item.isMultipleChoice && item.userAnswer))
								}">{{index+1}}</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	import uniIcons from '@/components/uni-icons/uni-icons';
	import request from '@/utils/request.js';

	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				exercisesType: '', // 练习类型
				rId: '', // 资源ID
				logId: null, // 做题记录ID
				subjectIds: [], // 考题ID数组
				currentSubjectIndex: 0, // 当前考题索引
				dataList: [], // 数据源
				loadedSubjects: new Map(), // 已加载的考题缓存 Map<id, subjectData>
				loadingSubjects: new Set(), // 正在加载的考题ID集合
				swiperList: [], // 轮播图数据列表
				swiperIndex: 0, // 轮播图当前位置
				isChange: false, // 是否切换
				topicIndex: 0, // 题目位置
				duration: 200, // 动画过渡时长
				showPopup: false, //弹窗是否显示
				userId: null, // 用户ID
				userType: '', // 用户类型
				answerQueue: [], // 答题请求队列
				isSubmitting: false // 标记是否正在提交
			}
		},
		computed: {
			// 结果统计
			countResult() {
				let [success, error] = [0, 0];

				this.dataList.forEach((item) => {
					if (item && item.isSubmitted && this.hasUserAnswer(item)) {
						if (item.isCorrect) {
							success++;
						} else {
							error++;
						}
					}
				});

				return {
					success,
					error
				}
			},

			// 当前题目的答题状态缓存
			currentItemAnswerStatus() {
				const currentItem = this.swiperList[this.swiperIndex];
				if (!currentItem) return {};

				const hasAnswered = this.getHasAnswered(currentItem);
				const isCorrect = hasAnswered ? this.isAnswerCorrectItem(currentItem) : false;

				return {
					hasAnswered,
					isCorrect,
					item: currentItem
				};
			}
		},
		async onLoad(options) {
			// 获取路由参数
			this.exercisesType = options.exercisesType || '';
			this.rId = options.rId || '';

			// 获取用户信息
			await this.$store.dispatch('GetInfo').then((res) => {
				this.userId = res.id;
				this.userType = res.userType;
			});

			// 开始获取数据流程
			await this.initExerciseData();
		},
		methods: {
			// 初始化练习数据
			async initExerciseData() {
				try {
					// 1. 调用 AddLog 接口创建做题记录
					const logResponse = await this.createExerciseLog();
					if (!logResponse) return;

					// 2. 根据 logId 获取考题ID数组
					const subjectIds = await this.getSubjectIdsByLogId(logResponse.id);
					if (!subjectIds || subjectIds.length === 0) return;

					// 3. 初始化数据列表占位符
					this.initDataListPlaceholders(subjectIds);

					// 4. 预加载前3题
					await this.preloadSubjects([0, 1, 2]);

					// 5. 渲染第一题
					this.renderSwiper(0);
				} catch (error) {
					console.error('初始化练习数据失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'error'
					});
				}
			},

			// 创建做题记录
			async createExerciseLog() {
				try {
					const response = await request({
						url: `/ApiCaSubject/AddLog`,
						method: 'POST',
						data: {
							exercisesType: this.exercisesType,
							rId: this.rId
						}
					});

					if (response) {
						this.logId = response.id;
						return response
					} else {
						uni.showToast({
							title: '加载失败',
							icon: 'error'
						});
						return null;
					}
				} catch (error) {
					console.error('创建做题记录失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'error'
					});
					return null;
				}
			},

			// 根据logId获取考题ID数组
			async getSubjectIdsByLogId(logId) {
				try {
					const response = await request({
						url: '/ApiCaSubject/GetByLogId',
						method: 'POST',
						data: {
							logId: logId
						}
					});

					if (response) {
						this.subjectIds = response;
						return response
					} else {
						uni.showToast({
							title: '获取考题列表失败',
							icon: 'error'
						});
						return null;
					}
				} catch (error) {
					console.error('获取考题ID数组失败:', error);
					uni.showToast({
						title: '获取考题列表失败',
						icon: 'error'
					});
					return null;
				}
			},

			// 初始化数据列表占位符
			initDataListPlaceholders(subjectIds) {
				this.dataList = subjectIds.map((id, index) => ({
					id: id,
					index: index,
					loading: false,
					loaded: false,
					subjectType: '加载中...',
					typeFullName: '',
					answer: '',
					answerSheet: [],
					userAnswer: null, // 用户选择的答案
					isSubmitted: false, // 是否已提交
					isCorrect: false, // 答案是否正确
					submittedAt: null, // 提交时间
					answerStatistics: {}, // 答案统计数据
					totalAnswerCount: 0, // 总答题人数
					subjectFile: null,
					analysisFile: null
				}));
			},

			// 预加载指定索引的考题
			async preloadSubjects(indices) {
				const loadPromises = indices
					.filter(index => index >= 0 && index < this.subjectIds.length)
					.map(index => this.loadSubjectByIndex(index));

				await Promise.allSettled(loadPromises);
			},

			// 根据索引加载考题
			async loadSubjectByIndex(index) {
				if (index < 0 || index >= this.subjectIds.length) return;

				const subjectId = this.subjectIds[index];

				// 检查是否已加载或正在加载
				if (this.loadedSubjects.has(subjectId) || this.loadingSubjects.has(subjectId)) {
					return;
				}

				// 标记为正在加载
				this.loadingSubjects.add(subjectId);
				this.dataList[index].loading = true;

				try {
					const subjectDetail = await this.getSubjectById(subjectId);
					if (subjectDetail) {
						// 缓存数据
						this.loadedSubjects.set(subjectId, subjectDetail);

						// 转换并更新数据
						const transformedData = this.transformSingleSubjectData(subjectDetail);
						this.$set(this.dataList, index, {
							...this.dataList[index],
							...transformedData,
							loading: false,
							loaded: true
						});
					}
				} catch (error) {
					console.error(`加载考题${subjectId}失败:`, error);
					this.dataList[index].loading = false;
				} finally {
					this.loadingSubjects.delete(subjectId);
				}
			},

			// 根据ID获取单个考题详情
			async getSubjectById(id) {
				try {
					const response = await request({
						url: '/ApiCaSubject/GetById',
						method: 'POST',
						data: {
							id: id
						}
					});

					if (response) {
						return response
					} else {
						console.error(`获取考题${id}失败`);
						return null;
					}
				} catch (error) {
					console.error(`获取考题${id}详情失败:`, error);
					return null;
				}
			},

			// 转换单个考题数据格式
			transformSingleSubjectData(item) {
				// 处理选项
				let answerSheet = [];
				if (item.option) {
					// 分割选项字符串
					const options = item.option.split(',');
					answerSheet = options.map((opt, index) => ({
						value: String.fromCharCode(65 + index), // A, B, C, D...
						name: opt.trim()
					}));
				}

				// 判断是否为多选题
				const isMultipleChoice = this.isMultipleChoiceQuestion(item.subjectType);

				return {
					id: item.id,
					subjectType: item.subjectType || '题目',
					typeFullName: item.typeFullName || '',
					answer: item.answer || '',
					answerSheet: answerSheet,
					userAnswer: isMultipleChoice ? [] : null, // 用户选择的答案
					isSubmitted: false, // 是否已提交
					isCorrect: false, // 答案是否正确
					submittedAt: null, // 提交时间
					answerStatistics: {}, // 答案统计数据
					totalAnswerCount: 0, // 总答题人数
					isMultipleChoice: isMultipleChoice, // 标记是否为多选题
					subjectFile: item.subjectFile, // 题目图片信息
					analysisFile: item.analysisFile // 解析图片信息
				};
			},

			// 判断是否为多选题
			isMultipleChoiceQuestion(subjectType) {
				if (!subjectType) return false;
				// 根据题目类型字段判断是否为多选题
				// 可以根据实际的题目类型标识进行调整
				return subjectType.includes('多选题')
			},

			// 判断选项是否被选中（多选题用）
			isOptionSelected(item, optionValue) {
				if (!item.isMultipleChoice) return false;
				return Array.isArray(item.answerResult) && item.answerResult.includes(optionValue);
			},

			// 判断选项是否为正确答案（多选题用）
			isCorrectAnswer(item, optionValue) {
				if (!item.isMultipleChoice) return false;
				// 假设多选题的正确答案用逗号分隔，如 "A,C,D"
				const correctAnswers = item.answer ? item.answer.split(',').map(a => a.trim()) : [];
				return correctAnswers.includes(optionValue);
			},





			// 判断是否有用户答案
			hasUserAnswer(item) {
				if (!item) return false;

				if (item.isMultipleChoice) {
					return Array.isArray(item.userAnswer) && item.userAnswer.length > 0;
				} else {
					return item.userAnswer !== null && item.userAnswer !== '';
				}
			},







			// 渲染 swiper
			renderSwiper(index) {
				// 确保索引在有效范围内
				if (index < 0) index = 0;
				if (index >= this.dataList.length) index = this.dataList.length - 1;

				let list = [];
				let current = 1;

				// 添加前一题（如果存在）
				if (index > 0 && this.dataList[index - 1]) {
					// 使用引用而不是拷贝，确保数据同步
					list.push(this.dataList[index - 1]);
				} else {
					current = 0;
				}

				// 添加当前题
				if (this.dataList[index]) {
					// 使用引用而不是拷贝，确保数据同步
					list.push(this.dataList[index]);
				}

				// 添加后一题（如果存在）
				if (index < this.dataList.length - 1 && this.dataList[index + 1]) {
					// 使用引用而不是拷贝，确保数据同步
					list.push(this.dataList[index + 1]);
				}

				this.duration = 0;

				setTimeout(() => {
					this.swiperList = list;
					this.swiperIndex = current;

					console.log('renderSwiper 完成：', {
						index: index,
						current: current,
						currentItem: this.swiperList[current],
						isSubmitted: this.swiperList[current]?.isSubmitted,
						userAnswer: this.swiperList[current]?.userAnswer
					});

					setTimeout(() => {
						this.duration = 200;
					}, 100)
				}, 50)
			},

			// 轮播图切换
			onChange(e) {
				// 非触摸事件不做轮播图状态更新
				if (e.detail.source != "touch") return;

				// 标识已切换
				this.isChange = true;

				// 计算新索引
				let newIndex = this.topicIndex;
				if (e.detail.current > this.swiperIndex) {
					newIndex++;
				} else {
					newIndex--;
				}

				// 边界检查
				if (newIndex < 0) newIndex = 0;
				if (newIndex >= this.dataList.length) newIndex = this.dataList.length - 1;

				this.topicIndex = newIndex;
				this.swiperIndex = e.detail.current;

				// 触发懒加载
				this.triggerLazyLoad(newIndex);
			},

			// 轮播图动画结束
			onAnimationfinish() {
				if (!this.isChange) return;

				this.isChange = false;
				setTimeout(() => {
					this.renderSwiper(this.topicIndex);
				}, 10);
			},

			// 选择题目
			pickerTopic(index) {
				// 边界检查
				if (index < 0) index = 0;
				if (index >= this.dataList.length) index = this.dataList.length - 1;

				this.topicIndex = index;
				this.renderSwiper(index);
				this.showPopup = false;

				// 触发懒加载
				this.triggerLazyLoad(index);
			},

			// 触发懒加载
			async triggerLazyLoad(currentIndex) {
				// 预加载当前题目及前后各2题
				const indicesToLoad = [];
				for (let i = currentIndex - 2; i <= currentIndex + 2; i++) {
					if (i >= 0 && i < this.dataList.length && !this.dataList[i].loaded && !this.dataList[i].loading) {
						indicesToLoad.push(i);
					}
				}

				if (indicesToLoad.length > 0) {
					await this.preloadSubjects(indicesToLoad);
					// 重新渲染当前swiper以更新数据
					this.renderSwiper(currentIndex);
				}
			},

			// 监听答题选择
			onAnswerSheet(index) {
				const currentItem = this.getCurrentItem();
				if (!currentItem || currentItem.isSubmitted) {
					return;
				}

				const selectedValue = currentItem.answerSheet[index].value;

				if (currentItem.isMultipleChoice) {
					this.toggleMultipleChoice(selectedValue);
				} else {
					this.selectSingleChoice(selectedValue);
				}
			},

			// 获取当前题目项
			getCurrentItem() {
				return this.swiperList[this.swiperIndex];
			},

			// 处理单选题选择
			selectSingleChoice(selectedValue) {
				const currentItem = this.getCurrentItem();
				if (!currentItem || currentItem.isSubmitted) {
					return;
				}

				// 直接设置用户答案
				this.updateUserAnswer(currentItem, selectedValue);
			},

			// 处理多选题切换
			toggleMultipleChoice(selectedValue) {
				const currentItem = this.getCurrentItem();
				if (!currentItem || currentItem.isSubmitted) {
					return;
				}

				// 确保用户答案是数组
				if (!Array.isArray(currentItem.userAnswer)) {
					currentItem.userAnswer = [];
				}

				// 创建新的答案数组
				const newAnswers = [...currentItem.userAnswer];
				const existingIndex = newAnswers.indexOf(selectedValue);

				if (existingIndex > -1) {
					// 取消选择
					newAnswers.splice(existingIndex, 1);
				} else {
					// 添加选择
					newAnswers.push(selectedValue);
				}

				// 更新用户答案
				this.updateUserAnswer(currentItem, newAnswers);
			},

			// 更新用户答案的统一方法
			updateUserAnswer(item, answer) {
				// 使用 Vue.set 确保响应式更新
				this.$set(item, 'userAnswer', answer);

				// 重置提交状态相关字段
				this.$set(item, 'isSubmitted', false);
				this.$set(item, 'isCorrect', false);
			},

			// 检查是否有用户答案
			hasUserAnswer(item) {
				if (!item) return false;

				if (item.isMultipleChoice) {
					return Array.isArray(item.userAnswer) && item.userAnswer.length > 0;
				} else {
					return item.userAnswer !== null && item.userAnswer !== undefined && item.userAnswer !== '';
				}
			},

			// 计算答案是否正确
			calculateAnswerCorrectness(item) {
				if (!item || !this.hasUserAnswer(item)) {
					return false;
				}

				if (item.isMultipleChoice) {
					// 多选题答案比较
					if (!item.answer) return false;

					const correctAnswers = item.answer.split(',')
						.map(a => a.trim())
						.filter(a => a)
						.sort();

					const userAnswers = [...item.userAnswer].sort();

					return correctAnswers.length === userAnswers.length &&
						correctAnswers.every(answer => userAnswers.includes(answer));
				} else {
					// 单选题答案比较
					return item.userAnswer === item.answer;
				}
			},

			// 提交当前题目答案
			async submitCurrentAnswer() {
				const currentItem = this.getCurrentItem();

				// 验证提交条件
				if (!this.validateSubmission(currentItem)) {
					return;
				}

				try {
					// 设置提交状态
					this.setSubmissionState(currentItem);

					// 计算答案正确性
					const isCorrect = this.calculateAnswerCorrectness(currentItem);
					this.$set(currentItem, 'isCorrect', isCorrect);

					// 提交到服务器
					await this.submitAnswerToServer(currentItem);

					// 加载答案统计数据
					await this.loadAnswerStatistics(currentItem);

					// 显示结果
					this.showSubmissionResult(isCorrect);

					// 记录提交日志
					this.logSubmissionResult(currentItem, isCorrect);

				} catch (error) {
					console.error('提交答案失败:', error);
					this.handleSubmissionError(currentItem, error);
				}
			},

			// 验证提交条件
			validateSubmission(item) {
				if (!item) {
					console.warn('提交失败: 当前题目不存在');
					return false;
				}

				if (item.isSubmitted) {
					console.warn('提交失败: 题目已经提交过');
					return false;
				}

				if (!this.hasUserAnswer(item)) {
					uni.showToast({
						title: '请先选择答案',
						icon: 'none',
						duration: 1500
					});
					return false;
				}

				return true;
			},

			// 设置提交状态
			setSubmissionState(item) {
				this.$set(item, 'isSubmitted', true);
				this.$set(item, 'submittedAt', new Date().toISOString());
			},

			// 提交答案到服务器
			async submitAnswerToServer(item) {
				// 格式化答案
				const formattedAnswer = this.formatAnswerForSubmission(item);

				// 调用提交接口
				await this.submitAnswer(item.id, formattedAnswer);
			},

			// 格式化答案用于提交
			formatAnswerForSubmission(item) {
				if (item.isMultipleChoice) {
					// 多选题：数组转为逗号分隔字符串
					return Array.isArray(item.userAnswer) ? item.userAnswer=item.userAnswer.sort().join(',') : '';
				} else {
					// 单选题：直接返回字符串
					return item.userAnswer || '';
				}
			},

			// 显示提交结果
			showSubmissionResult(isCorrect) {
				uni.showToast({
					title: isCorrect ? '回答正确！' : '回答错误！',
					icon: isCorrect ? 'success' : 'error',
					duration: 1500
				});
			},

			// 记录提交日志
			logSubmissionResult(item, isCorrect) {
				console.log('答案提交完成:', {
					id: item.id,
					type: item.isMultipleChoice ? '多选题' : '单选题',
					userAnswer: item.userAnswer,
					correctAnswer: item.answer,
					isCorrect: isCorrect,
					submittedAt: item.submittedAt
				});
			},

			// 处理提交错误
			handleSubmissionError(item, errorInfo) {
				console.error('提交答案错误:', errorInfo);

				// 回滚提交状态
				this.$set(item, 'isSubmitted', false);
				this.$set(item, 'submittedAt', null);

				// 显示错误提示
				uni.showToast({
					title: '提交失败，请重试',
					icon: 'error',
					duration: 2000
				});
			},

			// 获取答案统计数据
			async getAnswerStatistics(subjectId) {
				try {
					const response = await request({
						url: '/ApiCaSubject/GetAllUserAnswer',
						method: 'POST',
						data: {
							subjectId: subjectId
						}
					});

					if (response&& response.data) {
						return {
							statistics: response.data || {},
							totalCount: response.count || 0
						};
					} else {
						console.warn('获取答案统计失败:', response);
						return null;
					}
				} catch (error) {
					console.error('获取答案统计错误:', error);
					return null;
				}
			},

			// 加载并设置答案统计
			async loadAnswerStatistics(item) {
				if (!item || !item.id) return;

				console.log('开始加载答案统计:', item.id);

				const statistics = await this.getAnswerStatistics(item.id);
				if (statistics) {
					this.$set(item, 'answerStatistics', statistics.statistics);
					this.$set(item, 'totalAnswerCount', statistics.totalCount);

					console.log('答案统计加载成功:', {
						subjectId: item.id,
						statistics: statistics.statistics,
						totalCount: statistics.totalCount,
						itemAfterUpdate: {
							answerStatistics: item.answerStatistics,
							totalAnswerCount: item.totalAnswerCount
						}
					});
				} else {
					console.log('答案统计加载失败:', item.id);
				}
			},



			// 提交用户答案
			async submitAnswer(id, answer) {
				// 将请求加入队列
				this.answerQueue.push({
					id,
					answer
				});

				// 如果没有正在处理的请求，则开始处理队列
				if (!this.isSubmitting) {
					this.processAnswerQueue();
				}
			},

			// 处理答题队列
			async processAnswerQueue() {
				if (this.answerQueue.length === 0) {
					this.isSubmitting = false;
					return;
				}

				this.isSubmitting = true;
				const {
					id,
					answer
				} = this.answerQueue.shift();

				try {
					// 发送请求到真实的提交答案接口
					const response = await request({
						url: '/ApiCaSubject/AddSubjectUser',
						method: 'POST',
						data: {
							logId: this.logId,
							subjectId: id,
							answer: answer
						}
					});

					console.log('答案提交成功:', {
						subjectId: id,
						answer: answer,
						response: response
					});

					// 请求成功后处理下一个
					this.processAnswerQueue();
				} catch (error) {
					console.error('提交答案失败:', error);
					// 继续处理队列，避免卡住
					this.processAnswerQueue();
				}
			}
		}
	}
</script>

<style lang="scss">
	@import "@/lib/helang-flex.scss";

	uni-page-body {
		height: 100% !important;
	}

	page {
		height: 100% !important;
		display: inline;
	}

	// 容器
	.container {
		height: 100%;
		background-color: #fff;
		position: relative;
		color: #333;
		font-size: 28rpx;
	}

	// 轮播盒子
	.swiper-box {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 100rpx;
		height: auto;
		width: auto;
		z-index: 1;
	}

	.swiper {
		height: 100%;

		.swiper-scroll {
			height: 100%;
		}

		.swiper-item {
			background-color: #fff;
			box-sizing: border-box;
			height: 100%;

			.topic-content {
				padding: 30rpx;
				font-size: 32rpx;
				color: #333;
				line-height: 1.6;

				// 题目类型标签
				.type-label {
					background-color: #0089ff;
					color: #fff;
					border-radius: 6rpx;
					padding: 4rpx 10rpx;
					font-size: 24rpx;
				}

				// 加载状态容器
				.loading-container {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 200rpx;

					.loading-text {
						color: #999;
						font-size: 28rpx;
					}
				}

				// 答题卡
				.answer-sheet {
					margin: 40rpx -30rpx;

					.item {
						padding: 20rpx 30rpx;

						&:active {
							background-color: #f3f3f3;
						}

						+.item {
							margin-top: 0;
						}

						.icon {
							margin-right: 20rpx;
							box-shadow: 0 0 5px #999;
							width: 40rpx;
							height: 40rpx;
							font-size: 26rpx;

							// 单选题样式（圆形）
							&.radio {
								border-radius: 50%;

								&.selected {
									border: 2rpx solid #0089ff;
									background-color: #e6f3ff;
								}

								&.success {
									background-color: #0089ff;
									border: 2rpx solid #0089ff;
								}

								&.error {
									background-color: #f84d27;
									border: 2rpx solid #f84d27;
								}
							}

							// 多选题样式（方形）
							&.checkbox {
								border-radius: 6rpx;

								&.selected {
									border: 2rpx solid #0089ff;
									background-color: #e6f3ff;
								}

								&.success {
									background-color: #0089ff;
									border: 2rpx solid #0089ff;
								}

								&.error {
									background-color: #f84d27;
									border: 2rpx solid #f84d27;
								}

								&.missed {
									background-color: #fff3cd;
									border: 2rpx solid #ffc107;
									color: #856404;
								}
							}
						}

						// 选项内容布局
						.option-content {
							display: flex;
							justify-content: space-between;
							align-items: center;
							width: 100%;

							.option-text {
								flex: 1;
							}

							.option-statistics {
								display: flex;
								align-items: center;
								margin-left: 20rpx;
								font-size: 24rpx;
								color: #666;

								.statistics-count {
									margin-right: 10rpx;
									color: #0089ff;
									font-weight: bold;
								}

								.statistics-percentage {
									color: #999;
								}
							}
						}
					}

					// 提交按钮容器
					.submit-button-container {
						padding: 30rpx;
						text-align: center;
					}

					// 提交按钮
					.submit-button {
						background-color: #0089ff;
						color: #fff;
						border: none;
						border-radius: 8rpx;
						padding: 20rpx 60rpx;
						font-size: 32rpx;
						font-weight: bold;

						&:active {
							background-color: #0066cc;
						}
					}

					// 选项内容布局
					.option-content {
						display: flex;
						justify-content: space-between;
						align-items: center;
						width: 100%;

						.option-text {
							flex: 1;
						}

						.option-statistics {
							display: flex;
							align-items: center;
							margin-left: 20rpx;
							font-size: 24rpx;
							color: #666;

							.statistics-count {
								margin-right: 10rpx;
								color: #0089ff;
								font-weight: bold;
							}

							.statistics-percentage {
								color: #999;
							}
						}
					}
				}

				// 答题结果
				.answer-result {
					background-color: #f3f3f3;
					border-radius: 8rpx;
					padding: 20rpx 30rpx;
					font-size: 28rpx;
				}

				// 题目解析
				.answer-doubt {
					font-size: 28rpx;
					color: #8a6d3b;
					background-color: #fcf8e3;
					border: #faebcc solid 1px;
					margin-top: 40rpx;
					padding: 20rpx 30rpx;
					border-radius: 8rpx;
					word-break: break-all
				}
			}
		}
	}

	// 底部面板
	.panel-bottom {
		position: absolute;
		left: 0;
		bottom: 0;
		height: auto;
		width: 100%;
		z-index: 2;

		// 遮罩
		.shade {
			position: fixed;
			z-index: 9;
			background-color: rgba(33, 33, 33, 0.5);
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}

		// 内容
		.content {
			position: absolute;
			z-index: 10;
			bottom: 0;
			left: 0;
			width: 100%;
			height: auto;
			background-color: #fff;
		}

		// 操作栏
		.action-bar {
			height: 100rpx;
			width: 100%;
			box-sizing: border-box;
			border-top: #ddd solid 1px;
			font-size: 28rpx;
			padding: 0 20rpx;

			.success-num {
				color: #0089ff;
			}

			.error-num {
				color: #f84d27;
			}
		}

		// 题目列表
		.topic-list {
			height: 600rpx;

			.list-box {
				padding: 20rpx 20rpx 0 20rpx;
				font-size: 28rpx;
				color: #666;

				.list-item {
					padding-bottom: 20rpx;

					>view {
						width: 80rpx;
						height: 80rpx;
						background-color: #fff;
						border: #ddd solid 1px;
						border-radius: 50%;
						margin: 0 auto;

						&.active {
							border: #0089ff solid 1px;
						}

						&.success {
							background-color: #dbf2ff;
							color: #0089ff;
							border: none;

							&.active {
								border: #0089ff solid 1px;
							}
						}

						&.error {
							background-color: #ffeceb;
							color: #f84d27;
							border: none;

							&.active {
								border: #f84d27 solid 1px;
							}
						}

						&.answered {
							background-color: #fff7e6;
							color: #fa8c16;
							border: none;

							&.active {
								border: #fa8c16 solid 1px;
							}
						}
	  				}
				}
			}
		}
	}
</style>