<template>
	<view class="container">
		<!-- 6个大栏目按钮 -->
		<image class="button-image" src="/static/logo_black.png" style="margin-top: 4vh;width: 65%;" mode="widthFix">
		</image>
		<scroll-view class="button-container" scroll-y="true">
			<view class="button-item " v-for="(item, index) in buttons" :key="index" @click="navigateTo(item.path)">
				<!-- 使用图片代替文字 -->
				<image class="button-image clickPng" :src="item.isPressed ? item.pressedImage : item.defaultImage"
					mode="widthFix"></image>
			</view>
		</scroll-view>


				<uni-data-picker placeholder="" popup-title="请选择所在地区"  v-model="classes"
					@change="onchange" @nodeclick="onnodeclick" @popupopened="onpopupopened"
					@popupclosed="onpopupclosed">
				</uni-data-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 6个大栏目按钮数据
				buttons: [{
						defaultImage: '/static/images/chuqinguanli.png',
						path: '/pages/transcript/calendar/index',
					},
					{
						defaultImage: '/static/images/chengjichaxun.png',
						path: '/pages/transcript/score/index',
					},
					{
						defaultImage: '/static/images/xuexijihua.png',
						path: '/pages/studyPlan/index'
					},
					{
						defaultImage: '/static/images/shipinkecheng.png',
						path: '/pages/studyVideo/index',
					},
					{
						defaultImage: '/static/images/meirigongu.png',
						path: '/pages/dailyPractice/index',
					},
					{
						defaultImage: '/static/images/mutikuozhan.png',
						path: '/pages/examination/index',
					},
				],
				classes: '1-2',
				dataTree: [{
						text: "一年级",
						value: "1-0",
						children: [{
								text: "1.1班",
								value: "1-1"
							},
							{
								text: "1.2班",
								value: "1-2"
							}
						]
					},
					{
						text: "二年级",
						value: "2-0",
						children: [{
								text: "2.1班",
								value: "2-1"
							},
							{
								text: "2.2班",
								value: "2-2"
							}
						]
					},
					{
						text: "三年级",
						value: "3-0",
						disable: true
					}
				]
			}
	},
	computed: {
			dynamicEvent() {

				return 'click';
			}


		},
		methods: {
			// 导航到指定页面
			navigateTo(path) {
				if (path === '/pages/examination/index') {
					console.log('111')
				} else {
					uni.navigateTo({
						url: path
					});
				}
			},
			onnodeclick(e) {
				console.log(e);
			},
			onpopupopened(e) {
				console.log('popupopened');
			},
			onpopupclosed(e) {
				console.log('popupclosed');
			},
			onchange(e) {
				console.log('onchange:', e);
			}

		},
	};
</script>

<style>
	.clickPng:hover,
	.clickPng:active {
		filter: brightness(96%);
	}

	/* 页面容器 */
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		/* height: 170vh; */
		/* background-color: #f8f8f8; */
		background: linear-gradient(to bottom, #1989FA, rgba(25, 137, 250, 0));
		padding: 10px;

	}

	/* 按钮容器 */
	.button-container {
		border-radius: 20px;
		padding: 30px;
		margin-top: 4vh;
		height: 80vh;
		width: 96%;
		max-width: 600px;
		background-color: #FFFFFF;

	}

	/* 单个按钮 */
	.button-item {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		margin: 20px 0;
		/* padding: 10px; */
		border-radius: 12px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}

	.button-item:active {
		transform: scale(0.95);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/* 按钮图片 */
	.button-image {
		width: 100%;
		/* 图片宽度占满按钮 */
		height: 155px;
		/* 根据图片高度调整 */
		border-radius: 8px;
		/* 圆角 */
	}
</style>