<template>
	<view class="container">
		<!-- 6个大栏目按钮 -->
		<image class="button-image" src="/static/logo_black.png" style="margin-top: 4vh;width: 65%;" mode="widthFix">
		</image>
		<scroll-view class="button-container" scroll-y="true">
			<view class="button-item " v-for="(item, index) in buttons" :key="index" @click="navigateTo(item.path)">
				<!-- 使用图片代替文字 -->
				<image class="button-image clickPng" :src="item.isPressed ? item.pressedImage : item.defaultImage"
					mode="widthFix"></image>
			</view>
		</scroll-view>


				<!-- 科目类型选择器 -->
		<uni-data-picker
			v-if="showSubjectPicker"
			placeholder="请选择科目类型"
			popup-title="请选择科目类型"
			v-model="selectedSubject"
			:localdata="subjectData"
			@change="onSubjectChange"
			@nodeclick="onSubjectNodeClick"
			@popupopened="onSubjectPopupOpened"
			@popupclosed="onSubjectPopupClosed">
		</uni-data-picker>
		
		
	</view>
</template>

<script>
	import request from '@/utils/request'

	export default {
		data() {
			return {
				// 6个大栏目按钮数据
				buttons: [{
						defaultImage: '/static/images/chuqinguanli.png',
						path: '/pages/transcript/calendar/index',
					},
					{
						defaultImage: '/static/images/chengjichaxun.png',
						path: '/pages/transcript/score/index',
					},
					{
						defaultImage: '/static/images/xuexijihua.png',
						path: '/pages/studyPlan/index'
					},
					{
						defaultImage: '/static/images/shipinkecheng.png',
						path: '/pages/studyVideo/index',
					},
					{
						defaultImage: '/static/images/meirigongu.png',
						path: '/pages/dailyPractice/index',
					},
					{
						defaultImage: '/static/images/mutikuozhan.png',
						path: '/pages/examination/index',
					},
				],
				// 科目选择器相关数据
				showSubjectPicker: false,
				selectedSubject: '',
				subjectData: [],
				
			}
	},
	computed: {
			dynamicEvent() {

				return 'click';
			}


		},
		methods: {
			// 导航到指定页面
			navigateTo(path) {
				if (path === '/pages/examination/index') {
					// 显示科目类型选择器
					this.showSubjectPicker = true;
					this.fetchSubjectTypes();
				} else {
					uni.navigateTo({
						url: path
					});
				}
			},

			// 获取科目类型数据
			async fetchSubjectTypes() {
				try {
					
					const response = await request({
						url: '/ApiCaSubject/GetType',
						method: 'POST',
					});
					console.log(response)
					if (response.statusCode === 200 && response.data) {
						// 转换数据格式为 uni-data-picker 需要的格式
						this.subjectData = this.convertSubjectData(response.data);
					} else {
						uni.showToast({
							title: '获取科目类型失败',
							icon: 'error'
						});
					}
				} catch (error) {
					console.error('获取科目类型失败:', error);
					uni.showToast({
						title: '获取科目类型失败',
						icon: 'error'
					});
				}
			},

			// 转换科目数据格式为级联结构
			convertSubjectData(data) {
				// 过滤显示的数据
				const filteredData = data.filter(item => item.isShow);

				// 构建层级关系
				const buildTree = (items, parentId = 0) => {
					return items
						.filter(item => item.pId === parentId)
						.sort((a, b) => a.order - b.order)
						.map(item => {
							const node = {
								text: item.name,
								value: item.id.toString()
							};

							// 查找子节点
							const children = buildTree(items, item.id);
							if (children.length > 0) {
								node.children = children;
							}

							return node;
						});
				};

				return buildTree(filteredData);
			},

			// 科目选择器事件处理
			onSubjectChange(e) {
				console.log('科目选择变化:', e);
				this.selectedSubject = e;

				// 获取最后选择的值（最深层级的选择）
				let selectedId;
				if (Array.isArray(e)) {
					// 如果是数组，取最后一个值（最深层级）
					selectedId = e[e.length - 1];
				} else {
					// 如果是单个值
					selectedId = e;
				}

				// 只有选择了具体的科目才跳转（避免只选择了一级分类就跳转）
				if (selectedId) {
					// 选择完成后隐藏选择器并跳转到考试页面
					this.showSubjectPicker = false;
					uni.navigateTo({
						url: `/pages/examination/index?subjectId=${selectedId}`
					});
				}
			},

			onSubjectNodeClick(e) {
				console.log('科目节点点击:', e);
				// 如果点击的是叶子节点（没有子节点的节点），可以直接跳转
				// 这里可以根据需要添加额外的逻辑
			},

			onSubjectPopupOpened() {
				console.log('科目选择器打开');
			},

			onSubjectPopupClosed() {
				console.log('科目选择器关闭');
				this.showSubjectPicker = false;
			}

		},
	};
</script>

<style>
	.clickPng:hover,
	.clickPng:active {
		filter: brightness(96%);
	}

	/* 页面容器 */
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		/* height: 170vh; */
		/* background-color: #f8f8f8; */
		background: linear-gradient(to bottom, #1989FA, rgba(25, 137, 250, 0));
		padding: 10px;

	}

	/* 按钮容器 */
	.button-container {
		border-radius: 20px;
		padding: 30px;
		margin-top: 4vh;
		height: 80vh;
		width: 96%;
		max-width: 600px;
		background-color: #FFFFFF;

	}

	/* 单个按钮 */
	.button-item {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		margin: 20px 0;
		/* padding: 10px; */
		border-radius: 12px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}

	.button-item:active {
		transform: scale(0.95);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/* 按钮图片 */
	.button-image {
		width: 100%;
		/* 图片宽度占满按钮 */
		height: 155px;
		/* 根据图片高度调整 */
		border-radius: 8px;
		/* 圆角 */
	}
</style>