<template>
	<view class="container">
		<!-- 6个大栏目按钮 -->
		<image class="button-image" src="/static/logo_black.png" style="margin-top: 4vh;width: 65%;" mode="widthFix">
		</image>
		<scroll-view class="button-container" scroll-y="true">
			<view class="button-item " v-for="(item, index) in buttons" :key="index" @click="navigateTo(item.path)">
				<!-- 使用图片代替文字 -->
				<image class="button-image clickPng" :src="item.isPressed ? item.pressedImage : item.defaultImage"
					mode="widthFix"></image>
			</view>
		</scroll-view>

	</view>
</template>

<script>
	import request from '@/utils/request'

	export default {
		data() {
			return {
				// 6个大栏目按钮数据
				buttons: [{
						defaultImage: '/static/images/chuqinguanli.png',
						path: '/pages/transcript/calendar/index',
					},
					{
						defaultImage: '/static/images/chengjichaxun.png',
						path: '/pages/transcript/score/index',
					},
					{
						defaultImage: '/static/images/xuexijihua.png',
						path: '/pages/studyPlan/index'
					},
					{
						defaultImage: '/static/images/shipinkecheng.png',
						path: '/pages/studyVideo/index',
					},
					{
						defaultImage: '/static/images/meirigongu.png',
						path: '/pages/dailyPractice/index',
					},
					{
						defaultImage: '/static/images/mutikuozhan.png',
						path: '/pages/examination/index',
					},
				],
				
			}
	},
	computed: {
			dynamicEvent() {

				return 'click';
			}


		},
		methods: {
			// 导航到指定页面
			navigateTo(path) {

					uni.navigateTo({
						url: path
					});
				
			},

			

		},
	};
</script>

<style>
	.clickPng:hover,
	.clickPng:active {
		filter: brightness(96%);
	}

	/* 页面容器 */
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		/* height: 170vh; */
		/* background-color: #f8f8f8; */
		background: linear-gradient(to bottom, #1989FA, rgba(25, 137, 250, 0));
		padding: 10px;

	}

	/* 按钮容器 */
	.button-container {
		border-radius: 20px;
		padding: 30px;
		margin-top: 4vh;
		height: 80vh;
		width: 96%;
		max-width: 600px;
		background-color: #FFFFFF;

	}

	/* 单个按钮 */
	.button-item {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		margin: 20px 0;
		/* padding: 10px; */
		border-radius: 12px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}

	.button-item:active {
		transform: scale(0.95);
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/* 按钮图片 */
	.button-image {
		width: 100%;
		/* 图片宽度占满按钮 */
		height: 155px;
		/* 根据图片高度调整 */
		border-radius: 8px;
		/* 圆角 */
	}
</style>