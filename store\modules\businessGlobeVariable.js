 
const businessGlobeVariable = {
    namespaced: true,
    state: {
        // 日常点检
        timeRangeTypeCheckDaily: '',
        // 周期巡视
        timeRangeTypeInspectionDaily: '',
    },
    mutations:{
        SET_TIME_RANGE_TYPE_CHECK_DAILY: (state, timeRangeTypeCheckDaily) => {
            state.timeRangeTypeCheckDaily = timeRangeTypeCheckDaily
        },
        SET_TIME_RANGE_TYPE_INSPECTION_DAILY: (state, timeRangeTypeInspectionDaily) => {
            debugger
            state.timeRangeTypeInspectionDaily = timeRangeTypeInspectionDaily
        },
    },
    actions:{
        SetTimeRangeTypeCheckDaily({ commit }, timeRangeTypeCheckDaily) {
            commit('SET_TIME_RANGE_TYPE_CHECK_DAILY', timeRangeTypeCheckDaily);
        },
        SetTimeRangeTypeInspectionDaily({ commit }, timeRangeTypeInspectionDaily) {
            debugger
            commit('SET_TIME_RANGE_TYPE_INSPECTION_DAILY', timeRangeTypeInspectionDaily);
        },
    },
    getters: {
        timeRangeTypeCheckDaily: state => state.timeRangeTypeCheckDaily,
        timeRangeTypeInspectionDaily: state => state.timeRangeTypeInspectionDaily,
    }
}
export default businessGlobeVariable