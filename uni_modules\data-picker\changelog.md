## 1.0.5（2023-06-29）
FIX

​		修复选择器在支付宝小程序的样式兼容问题
## 1.0.4（2023-06-27）

支持异步加载数据，动态页面渲染，可动态修改indexs，defaultIds，defaultNames，sourceData。使用时父页面直接修改数据即可，具体可看示例
## 1.0.3（2023-06-27）

新增了initBack属性，默认为true，返回根据默认值加载的数据，相当于初始化后手动点了一次确认，获取数据同样在confirm里面，具体可看示例
## 1.0.2（2023-06-26）

FIX

​		修复vue2中的语法警告

​		修复小程序中的样式兼容问题

ADD

​		考虑到业务复杂性，返回数据中新增了所选择的对象
## 1.0.1（2023-06-21）

当对象显示属性不是name时，支持自定义修改显示的属性名，eg：
    我要显示对象中的areaName属性，我可以设置labelName="areaName"
    具体使用可以看示例工程

## 1.0.0（2023-06-20）
基于uview封装的级联选择器，内置的地区数据

支持单列，多列，自动识别层级

支持自动去除重复名称：北京市-北京市-东城区---->北京市东城区

支持操作按钮显示在顶部，或者底部

支持三种模式传递默认值，如下

​      index:[0,0,7],

​      defaultIds: [1, 110000, 110106],

​      defaultNames: ['北京市', '北京市', '房山区']
