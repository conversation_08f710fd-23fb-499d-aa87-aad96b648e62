<template>
	<view class="detail-page">
		<!-- 头部区域 -->
		<view class="detail-header">

			<text class="title">试卷</text>
		</view>

		<!-- 两栏布局 -->
		<view class="detail-content">
			<!-- 上栏：成绩细则 -->
			<view class="score-detail">
				<text class="section-title">成绩细则</text>
				<view class="grid-container">
					<view class="grid-item" v-for="(info, index) in formatScores">
						<view class="icon-container">
							<image class="icon" :src="info.icon" mode="aspectFit" />
						</view>
						<view class="icon-container"
							style="display: flex;justify-content: center;align-items: center;flex-direction: column;">
							<text class="label">{{ info.name }}</text>
							<view class="score-text-container">
								<text class="score-value">{{ info.totalScore }}</text>
								<text class="value">/ null</text>
							</view>
						</view>
						<!-- <image :src="info.icon" mode="aspectFit" /> -->
					</view>
				</view>
			</view>

			<!-- 下栏：试卷分析 -->
			<view class="paper-analysis">
				<text class="section-title">试卷分析</text>
				<view class="file-section">
					<!-- 左侧：浮动上传按钮 -->
					<!-- <button class="upload-btn" @click="chooseFile">上传文件</button> -->
					<!-- 右侧：查看文件按钮框 -->
					<view class="view-file-box">
						<text class="file-tip">试卷文件：{{ scoreDetails.fileName ? scoreDetails.fileName : '' }}</text>
						<button class="view-btn" @click="viewFile" v-if="scoreDetails.filePath">查看文件</button>
						<text class="no-file" v-else>暂无文件</text>
					</view>
				</view>
			</view>
		</view>
		<uni-fab direction="horizontal" ref="uniFab" :pattern="pattern" horizontal="left" vertical="bottom"
			@trigger="trigger" :content="buttons" v-show="isTeacher" />
	</view>
</template>

<script>
import storage from '../../../utils/storage';
import request from '@/utils/request'
import constant from '@/utils/constant'
import user from '../../../store/modules/user';
import upload from '../../../utils/upload';
import download from '../../../utils/download';
import chooseFile from '../../../utils/chooseFile';
export default {
	data() {
		return {
			id: 0, // 用户 ID
			scoreDetails: {}, // 成绩细则数据
			fileUrl: '', // 文件 URL
			formatScores: [], //成绩细则格式化数据
			pattern: {
				color: '#7A7E83',
				backgroundColor: '#fff',
				selectedColor: '#FF5733',
				buttonColor: '#FFE4B5',
				iconColor: '#fff'
			},
			isTeacher: true,
			buttons: [{
				text: '上传图片',
				active: false
			},
			{
				text: '上传PDF',
				active: false
			},
			]
		};
	},
	mounted() {
		this.isTeacher = storage.get(constant.roles).includes('管理员')
		// const fab = this.$refs.uniFab.$el;
		// fab.style.position = 'fixed';
		// fab.style.left = '10px'; // 距离左侧 10px，可以根据需要调整
		// fab.style.top = '50%';
		// fab.style.transform = 'translateY(-50%)';

		// this.isTeacher = user.roles.includes('管理员');

	},
	onLoad(options) {
		this.id = options.id; // 从路由参数中获取用户 ID
		this.fetchScoreDetails(); // 获取成绩细则数据
		//this.fetchFileInfo(); // 获取文件信息

	},
	methods: {
		// 请求后端 API 获取成绩细则数据
		async fetchScoreDetails() {
			try {
				const response = await request({
					url: '/ApiCaScore/GetById', // 替换为你的成绩细则 API 地址
					method: 'POST',
					data: {
						id: this.id,
					},
				});
				console.log(response.data)
				if (response.code === 200) {

					this.scoreDetails = response.data; // 假设返回的数据格式为 { data: [] }
					this.formatData(response.data)
				} else {
					console.error('获取成绩细则失败:', response.data);
					uni.showToast({
						title: '获取成绩细则失败',
						icon: 'none',
					});
				}
			} catch (error) {
				console.error('请求失败:', error);
				uni.showToast({
					title: '请求失败',
					icon: 'none',
				});
			}
		},
		// 格式化数据
		formatData(data) {
			const newData = [];
			newData.push({
				name: '阅读',
				totalScore: data.readA + data.readB || 'null',
				ranking: data.englishRanking || 'null',
				score: data.english || 'null',
				icon: '/static/images/subject/img_en.png',
				color: '#00FF7F'
			})
			newData.push({
				name: '数学',
				totalScore: data.maths == 0 ? 0 : data.maths || 'null',
				ranking: data.englishRanking || 'null',
				score: data.maths || 'null',
				icon: '/static/images/subject/img_maths.png',
				color: '#87CEEB'
			})
			newData.push({
				name: '作文',
				totalScore: (data.smallComposition + data.bigComposition) == 0 ? 0 : data.smallComposition + data.bigComposition || 'null',
				ranking: data.englishRanking || 'null',
				score: data.maths || 'null',
				icon: '/static/images/subject/img_en.png',
				color: '#87CEEB'
			})
			newData.push({
				name: '逻辑',
				totalScore: data.logic == 0 ? 0 : data.logic || 'null',
				ranking: data.logicRanking || 'null',
				score: data.logic || 'null',
				icon: '/static/images/subject/img_logic.png',
				color: '#FFD700'
			})
			newData.push({
				name: '翻译',
				totalScore: data.translate==0? 0: data.translate || 'null',
				ranking: data.logicRanking || 'null',
				score: data.logic || 'null',
				icon: '/static/images/subject/img_en.png',
				color: '#FFD700'
			})
			newData.push({
				name: '完型',
				totalScore: data.gestalt==0? 0: data.gestalt || 'null',
				ranking: data.logicRanking || 'null',
				score: data.logic || 'null',
				icon: '/static/images/subject/img_logic.png',
				color: '#FFD700'
			})
			newData.push({
				name: '论效',
				totalScore: data.onEffectiveWriting==0? 0: data.onEffectiveWriting || 'null',
				ranking: data.logicRanking || 'null',
				score: data.logic || 'null',
				icon: '/static/images/subject/img_manager.png',
				color: '#FFD700'
			})
			newData.push({
				name: '论说',
				totalScore: data.essay == 0 ? 0 : data.essay || 'null',
				ranking: data.manageRanking || 'null',
				score: data.ManageComprehensive || 'null',
				icon: '/static/images/subject/img_manager.png',
				color: '#00008B'
			})
			this.formatScores = newData;
			// {
			//         "createBy": "管理员",
			//         "createTime": "2025-03-14 17:09:49",
			//         "updateBy": "管理员",
			//         "updateTime": "2025-03-14 17:10:00",
			//         "remark": null,
			//         "id": 1,
			//         "userId": 0,
			//         "userName": "吴雨晴",
			//         "testPaperCode": "H1",
			//         "rankUpOrDown": 1,
			//         "englishTotalScore": 50,
			//         "englishRanking": 1,
			//         "gestalt": 30,
			//         "readA": 20,
			//         "readB": 20,
			//         "translate": 0,
			//         "smallComposition": 0,
			//         "bigComposition": 0,
			//         "manageComprehensive": 46,
			//         "manageRanking": 3,
			//         "maths": 30,
			//         "mathsRanking": 1,
			//         "logic": 20,
			//         "logicRanking": 1,
			//         "essay": 10,
			//         "onEffectiveWriting": 20,
			//         "totalScore": null,
			//         "ranking": null
			//     }
		},
		// 处理按钮点击事件
		trigger(e) {
			const text = e.item.text
			switch (text) {
				case '上传图片':
					this.chooseImg()
					break;
				case '上传文件':
					this.chooseFile()
					break;
				default:
					break;
			}
		},

		// 请求后端 API 获取文件信息
		async fetchFileInfo() {
			try {
				const response = await download({
					url: '/api/transcript/score/download?fileName=' + this.scoreDetails.filePath,
				});

				if (response.statusCode === 200) {
					// this.scoreDetails.filePath = response.data.fileUrl; // 假设返回的数据格式为 { fileUrl: '...' }
				} else {
					console.error('获取文件信息失败:', response.data);
					uni.showToast({
						title: '获取文件信息失败',
						icon: 'none',
					});
				}
			} catch (error) {
				console.error('请求失败:', error);
				uni.showToast({
					title: '请求失败',
					icon: 'none',
				});
			}
		},

		// 选择文件
		chooseFile() {
			if (plus.os.name == 'Android') {
				chooseFile((path) => {
					this.uploadFile(path)
				})
			} else {
				uni.chooseFile({
					count: 1, // 最多选择 1 个文件
					type: 'all',
					success: (res) => {
						const file = res.tempFiles[0];
						this.uploadFile(file); // 上传文件
					},
					fail: (err) => {
						console.error('选择文件失败:', err);
						uni.showToast({
							title: '选择文件失败',
							icon: 'none',
						});
					},
				});
			}
		},

		// 选择图片
		chooseImg() {
			uni.chooseImage({
				count: 1, // 最多选择 1 个文件
				sourceType: ['album', 'camera'],
				success: (res) => {
					const file = res.tempFiles[0];
					this.uploadFile(file.path); // 上传文件
				},
				fail: (err) => {
					console.error('选择文件失败:', err);
					uni.showToast({
						title: '选择文件失败',
						icon: 'none',
					});
				},
			});
		},

		// 上传文件
		async uploadFile(path) {
			console.log(path);
			try {
				uni.showLoading({
					title: '上传仲...',
					mask: true
				});
				const response = await upload({
					url: '/api/transcript/score/upload',
					filePath: path,
					formData: {
						id: this.id
					},
				});
				uni.hideLoading()
				console.log("response:" + response);
				if (response.statusCode == 200) {
					this.fetchScoreDetails()
				} else {
					console.error('上传文件失败:', response);
					uni.showToast({
						title: '上传文件失败',
						icon: 'none',
					});
				}
			} catch (error) {
				console.error('上传失败:', error);
				uni.showToast({
					title: '上传失败',
					icon: 'none',
				});
			}
		},

		// 查看文件
		async viewFile() {
			if (this.scoreDetails.filePath) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				console.log(this.scoreDetails.filePath);
				try {
					const response = await download({
						url: '/api/transcript/score/download?fileName=' + this.scoreDetails.filePath,
					});
					uni.hideLoading();
					if (response.statusCode == 200) {
						console.log("下载文件成功:" + response.tempFilePath + "  code:" + response.statusCode);
						uni.saveFile({
							tempFilePath: response.tempFilePath,
							success: (saveRes) => {
								console.log("保存文件成功:" + JSON.stringify(saveRes));
								uni.openDocument({
									filePath: saveRes.savedFilePath,
									success: () => {
										console.log('打开文件成功');
									},
									fail: (err) => {
										console.error('打开文件失败:', err);
										uni.showToast({
											title: '打开文件失败',
											icon: 'none',
										});
									},
								});
							},
							fail(saveErr) {
								console.error('保存文件失败:', saveErr);
								uni.showToast({
									title: '保存文件失败',
									icon: 'none',
								});
							}

						})
					} else {
						console.error('下载文件失败:', response);
						uni.showToast({
							title: '下载文件失败',
							icon: 'none',
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('下载文件失败:', error);
					uni.showToast({
						title: '下载文件失败',
						icon: 'none',
					});
				}
			} else {
				uni.showToast({
					title: '文件不存在',
					icon: 'none',
				});
			}
		},
	},
};
</script>


<style scoped>
.score-text-container {
	flex-direction: row;
}

.score-value {
	font-weight: bold;
	font-size: 20px;
	color: #fff;
}

.detail-page {
	padding: 20px;
}

/* 头部区域 */
.detail-header {
	position: relative;
	text-align: center;
	margin-bottom: 20px;
}

.back-btn {
	position: absolute;
	left: 10px;
	top: 0;
}

.title {
	font-size: 24px;
	font-weight: bold;
}

/* 两栏布局 */
.detail-content {
	display: flex;
	flex-direction: column;
	/* 纵向布局 */
	gap: 20px;
}

/* 上栏：成绩细则 */
.score-detail {
	width: 100%;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 10px;
	display: block;
}

.grid-container {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	/* 2 列 */
	gap: 10px;
}

.grid-item {
	position: relative;
	aspect-ratio: 1;
	/* 正方形 */
	background-color: #fff;
	border-radius: 12px;
	/* 圆角 */
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
	/* 阴影效果 */
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 10px;
}

.icon-container {
	position: absolute;
	top: 0px;
	left: 0px;
	right: 0px;
	bottom: 0px;
}

.icon {
	width: 100%;
	height: 100%;
}

.label {
	font-size: 35px;
	font-weight: bold;
	color: white;
	/* 与图标保持距离 */
	margin-bottom: 20px;
}

.value {
	font-size: 16px;
	font-weight: bold;
	color: #999;
}

/* 下栏：试卷分析 */
.paper-analysis {
	width: 100%;
}

.file-section {
	display: flex;
	align-items: center;
	gap: 20px;
}

.upload-btn {
	background-color: #007AFF;
	color: #fff;
	font-size: 14px;
	padding: 8px 16px;
	border-radius: 4px;
}

.view-file-box {
	flex-direction: column;
	flex: 1;
	display: flex;
	align-items: center;
	gap: 10px;
}

.file-tip {
	font-size: 14px;
	color: #666;
}

.view-btn {
	background-color: #4CAF50;
	color: #fff;
	font-size: 14px;
	padding: 4px 8px;
	border-radius: 4px;
}

.no-file {
	font-size: 14px;
	color: #888;
}
</style>