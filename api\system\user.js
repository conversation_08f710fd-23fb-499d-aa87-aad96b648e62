import upload from '@/utils/upload'
import download from '@/utils/download'
import request from '@/utils/request'

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
	const data = {
		oldPassword,
		newPassword
	}
	return request({
		url: '/ApiSysUser/UpdatePwd',
		method: 'post',
		data: data
	})
}

// 查询用户个人信息
export function getUserInfo() {
	return request({
		url: '/ApiSysUser/GetUserInfo',
		method: 'get'
	})
}

// 查询用户列表
export function getUserList(userName,count) {
	const data = {
		userName,//搜索用户名
		count//返回结果数量（默认：0，不限）
	}
	return request({
		url: '/ApiSysUser/GetList',
		method: 'post',
		data: data
	})
}


// 修改用户个人信息
export function updateUserProfile(data) {
	return request({
		url: '/ApiSysUser/Edit',
		method: 'post',
		data: data
	})
}

// 用户头像上传
export function uploadAvatar(data) {
	return upload({
		url: '/ApiSysUser/UpdateAvatar',
		name: data.name,
		filePath: data.filePath
	})
}

// 文件下载（测试用）
export function downloadFile(data) {
	return download({
		url: '/ApiSysUser/DownloadFile',
		params: data,
	})
}