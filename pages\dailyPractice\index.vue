<template>
	<view class="container">


		<view class="container_title">
			<view>
				<view class="container_title_text1">{{Text1}}</view>
				<view class="container_title_text2">{{Text2}}</view>
			</view>
		</view>
		<view class="">
			<liusheng22-switchBlock :switchList="['我的考勤','考勤总览']" :width="500" :height="80"></liusheng22-switchBlock>
		</view>



		<liu-calendar @change="getDate" @checked="checkDate" :data="calendarData"></liu-calendar>
	</view>
</template>

<script>
	import request from '@/utils/request'
	import {
		checkRole
	} from '@/utils/permission.js'
	import storage from '@/utils/storage'
	import constant from '@/utils/constant'
	import {
		getUserList
	} from '@/api/system/user.js';
	export default {
		data() {
			return {
				userName: '', // 用户名称
				userNamekey: '', //搜索关键字
				isTeacher: false,
				Text1: '',
				Text2: '',
				showSuggestions: false, // 控制建议列表的显示
				suggestions: [], // 存储姓名建议
				calendarData: {},
			}
		},
		onShow() {
			console.log('onshow')
			this.fetchData()
		},
		mounted() {
			this.isTeacher = checkRole(['管理员', '教师']);
			if (this.isTeacher) {
				this.userName = ''
			} else {
				this.userName = storage.get(constant.name)

				this.userNamekey = storage.get(constant.name)

			}
			this.fetchData()
		},
		methods: {

			//获取选中的月份
			getDate(e) {
				console.log('选中的月份：' + e.value)
			},
			// 获取选中的日期
			checkDate(e) {
				console.log('选中的日期：' + e.value);

				// 获取当前日期（仅年月日）
				const now = new Date();
				const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

				// 将选中的日期转换为仅年月日的格式
				const selectedDateStr = e.value;
				const selectedDateParts = selectedDateStr.split('-');
				if (selectedDateParts.length !== 3) {
					uni.showToast({
						title: '日期格式错误',
						icon: 'none'
					});
					return;
				}

				const year = parseInt(selectedDateParts[0]);
				const month = parseInt(selectedDateParts[1]) - 1; // 月份从0开始
				const day = parseInt(selectedDateParts[2]);

				const selectedDate = new Date(year, month, day);

				// 比较日期（仅年月日）
				if (selectedDate > today) {
					uni.showToast({
						title: '不能选择未来日期',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				this.fetchGetByDate(e.value);
			},

			onSearchConfirm(res) {
				this.userName = res.value
				this.showSuggestions = false
				console.log('执行搜索:', res.value)
				this.fetchData()
			},
			cancel() {
				this.userName = ''
				this.suggestions = []
				this.showSuggestions = false
				this.fetchData()
			},
			// 处理搜索框输入事件，用于获取姓名建议
			onSearchInput(e) {
				console.log('搜索输入:', e);
				// 清除之前的定时器
				if (this.debounceTimer) {
					clearTimeout(this.debounceTimer)
				}

				// 设置新的定时器，实现防抖
				this.debounceTimer = setTimeout(() => {
					const value = e; // 直接使用传入的值而不是this.userName
					if (value && value.length > 0) {
						this.fetchUserNameSuggestions(value)
					} else {
						this.suggestions = []
						this.showSuggestions = false
					}
				}, 300) // 300ms的防抖延迟
			},
			// 请求姓名建议列表
			async fetchUserNameSuggestions(name) {
				console.log('开始获取建议, 输入值:', name);
				if (!name || name.trim() === '') {
					this.suggestions = [];
					this.showSuggestions = false;
					return;
				}
				//代码优化
				getUserList(name, 10)
					.then((response) => {
						console.log('姓名建议:', response);
						if (response && Array.isArray(response)) {
							this.suggestions = response;
							this.showSuggestions = this.suggestions.length > 0;
						} else {
							this.suggestions = [];
							this.showSuggestions = false;
						}
					})
					.catch((error) => {
						console.error('获取姓名建议失败:', error);
						this.suggestions = [];
						this.showSuggestions = false;
					});

			},
			// 选择一个姓名建议
			selectSuggestion(suggestion) {
				console.log(suggestion)

				this.userName = suggestion.userName
				this.showSuggestions = false
				this.fetchData()
			},
			getDates() {
				const now = new Date();

				// 获取当日日期（格式：YYYY-MM-DD）
				const today = this.formatDate(now);

				// 获取本月第一天（格式：YYYY-MM-01）
				const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
				const formattedFirstDay = this.formatDate(firstDayOfMonth);

				return {
					today,
					firstDayOfMonth: formattedFirstDay
				};
			},
			formatDate(date) {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			// 请求后端 API 获取当日考题
			async fetchGetByDate(e) {
				try {
					const response = await request({
						url: '/ApiCaSubjectDay/GetByDate',
						method: 'POST',
						data: {
							subjectDate: e,
							courseType: this.Text1
						},
					});
					if (response.length > 0) {
						uni.navigateTo({
							url: `/pages/answer/index?e=${e}`
						})


					} else {
						uni.showToast({
							title: '暂无考题',
							icon: 'error',
						})
						this.timetables = []
					}
					

				} catch (error) {
					console.error('请求失败:', error)
					uni.showToast({
						title: '考题加载失败',
						icon: 'none',
					})
				}
			},

			// 请求后端 API 获取做题数据
			async fetchData() {
				try {
					//
					let id = ''
					await this.$store.dispatch('GetInfo').then((res) => {
						console.log('获取用户信息结果', res.id);
						this.Text1 = res.userType
						this.Text2 = res.userName
						id = res.id
					});

					if (this.userId) {
						console.log('获取用户信息结果', this.userId);
						id = this.userId;
					}
					console.log('id:' + id);
					const {
						today,
						firstDayOfMonth
					} = this.getDates()
					const response = await request({
						url: '/ApiCaSubjectDay/GetUserList',
						method: 'POST',
						data: {
							beginTime: firstDayOfMonth,
							endTime: today,
							courseType: this.Text1,
							userId: id,
						},
					});
					if (response.length > 0) {
						let obj={}
						response.map((i)=>{
							// this.calendarData.set(i.subjectDate.split(" ")[0],i.state==='已答'?0:1)
							obj[i.subjectDate.split(" ")[0]]=i.state==='已答'?0:1
						})

						this.calendarData=obj
					} else {
						this.timetables = []
					}
					
					console.log("response", response);


				} catch (error) {
					console.error('请求失败:', error)
			
					uni.showToast({
						title: '请求失败',
						icon: 'none',
					})
				}
			},
		}
	}
</script>

<style>
	.uni-searchbar__cancel {
		color: #1D89FF;
	}

	/* 姓名建议列表样式 */
	.suggestion-container {
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 14px;
		margin: 0 15px;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
		max-height: 200px;
		overflow-y: auto;
		z-index: 100;
		position: relative;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.8);
		transform: translateY(5px);
		animation: fadeIn 0.3s ease-out forwards;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(10px);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.suggestion-item {
		padding: 14px 18px;
		border-bottom: 1px solid rgba(240, 240, 240, 0.8);
		font-size: 14px;
		transition: all 0.25s ease;
		position: relative;
		overflow: hidden;
	}

	.suggestion-item:last-child {
		border-bottom: none;
	}

	.suggestion-item:active {
		background-color: rgba(245, 247, 250, 0.8);
	}

	.suggestion-item::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 0;
		background: linear-gradient(90deg, #6a89cc, #4a69bd);
		z-index: -1;
		transition: height 0.25s ease;
		opacity: 0;
	}

	.suggestion-item:hover {
		color: #4a69bd;
		padding-left: 24px;
	}

	.suggestion-item:hover::after {
		height: 3px;
		opacity: 1;
	}

	/* 页面容器 */
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: start;

		height: 100vh;
		/* background-color: #f8f8f8; */
		background: linear-gradient(to bottom, #1D89FF, rgba(25, 137, 250, 0));
		padding: 10px;

	}

	.container_title {
		width: 100%;
		font-weight: 400;
		font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
		padding-left: 5%;
		padding-bottom: 17%;
		padding-top: 5vh;

		background-image: url("/static/images/down.png");
		background-position: right;
		background-repeat: no-repeat;
		background-size: 65% auto;
	}

	.container_title_text1 {
		color: #FFF;
		font-size: 24px;
		margin-bottom: 3%;

	}

	.container_title_text2 {
		color: #FFF;
		font-size: 18px;

	}
</style>