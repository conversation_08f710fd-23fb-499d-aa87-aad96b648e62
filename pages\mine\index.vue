<template>
	<view class="mine-container" :style="{ height: `${windowHeight}px` }">
		<!--顶部个人信息栏-->
		<view class="header-section">
			<view class="flex padding justify-between">
				<view class="flex align-center">
					<view v-if="!avatar" class="cu-avatar xl round bg-white">
						<view class="iconfont icon-people text-gray icon"></view>
					</view>
					<image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix"></image>
					<view v-if="!name" @click="handleToLogin" class="login-tip">点击登录</view>
					<view v-if="name" @click="handleToInfo" class="user-info">
						<view class="u_title">用户名：{{ name }}</view>
					</view>
				</view>
				<view @click="handleToInfo" class="flex align-center">
					<text>个人信息</text>
					<view class="iconfont icon-right"></view>
				</view>
			</view>
		</view>

		<view class="content-section">
			<!-- <view class="mine-actions grid col-4 text-center">

        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-service text-blue icon"></view>
          <text class="text">在线客服</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-community text-mauve icon"></view>
          <text class="text">反馈社区</text>
        </view>
        <view class="action-item" @click="handleBuilding">
          <view class="iconfont icon-dianzan text-green icon"></view>
          <text class="text">点赞我们</text>
        </view>
      </view> -->

			<view class="menu-list">
				<view class="list-cell list-cell-arrow" @click="handleToEditInfo">
					<view class="menu-item-box">
						<view class="iconfont icon-user menu-icon"></view>
						<view>编辑资料</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleHelp">
					<view class="menu-item-box">
						<view class="iconfont icon-help menu-icon"></view>
						<view>常见问题</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleAbout">
					<view class="menu-item-box">
						<view class="iconfont icon-aixin menu-icon"></view>
						<view>关于我们</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleToSetting">
					<view class="menu-item-box">
						<view class="iconfont icon-setting menu-icon"></view>
						<view>应用设置</view>
					</view>
				</view>
				<view class="list-cell list-cell-arrow" @click="handleDownload">
					<view class="menu-item-box">
						<view class="iconfont icon-setting menu-icon"></view>
						<view>下载文件</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import storage from '@/utils/storage';
import { downloadFile } from '@/api/system/user';
import { tansParams } from '@/utils/common';
import { getToken } from '@/utils/auth';

export default {
	data() {
		return {
			name: this.$store.state.user.name,
			version: getApp().globalData.config.appInfo.version,
		};
	},
	computed: {
		avatar() {
			return this.$store.state.user.avatar;
		},
		windowHeight() {
			return uni.getSystemInfoSync().windowHeight - 50;
		},
	},
	methods: {
		handleToInfo() {
			this.$tab.navigateTo('/pages/mine/info/index');
		},
		handleToEditInfo() {
			this.$tab.navigateTo('/pages/mine/info/edit');
		},
		handleToSetting() {
			this.$tab.navigateTo('/pages/mine/setting/index');
		},
		handleDownload() {
			const data = { userName: '梁工' };
			console.log('请求下载参数', data);

			downloadFile(data).then((res) => {
				console.log('请求下载结果', res);
				// 下载成功后的处理逻辑
				if (res.statusCode === 200) {
					// 临时文件路径
					const tempFilePath = res.tempFilePath;
					console.log('临时文件路径:', tempFilePath);
					// // 获取当前运行环境信息
					//       const systemInfo = uni.getSystemInfoSync();
					// 	  console.log('systemInfo',systemInfo);
					//       const isH5 = systemInfo.platform === 'h5'; // 判断是否是 H5 环境
					//       const isApp = systemInfo.platform === 'android' || systemInfo.platform === 'ios'; // 判断是否是 App 环境

					// 检查是否支持 uni.saveFile（浏览器下不支持需要注销代码）
					if (typeof uni.saveFile === 'function') {
						console.log('支持 uni.saveFile');
						// 在 HBuilderX 环境中保存文件
						uni.saveFile({
							tempFilePath: tempFilePath,
							success: function (saveRes) {
								console.log('文件已保存:', saveRes.savedFilePath);
								uni.showToast({
									title: '文件已保存',
									icon: 'success',
								});
							},
							fail: function (err) {
								console.error('保存文件失败:', err);
								uni.showToast({
									title: '保存文件失败',
									icon: 'none',
								});
							},
						});
					} else {
						// 在浏览器环境中创建下载链接
						const link = document.createElement('a');
						link.href = tempFilePath;
						link.download = 'dynamic_file.xlsx'; // 设置下载的文件名
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
					}
				} else {
					console.error('下载失败，状态码:', res.statusCode);
					uni.showToast({
						title: '下载失败',
						icon: 'none',
					});
				}
			});
		},
		handleToLogin() {
			this.$tab.reLaunch('/pages/login');
		},
		handleToAvatar() {
			this.$tab.navigateTo('/pages/mine/avatar/index');
		},
		handleLogout() {
			this.$modal.confirm('确定注销并退出系统吗？').then(() => {
				this.$store.dispatch('LogOut').then(() => {
					this.$tab.reLaunch('/pages/index');
				});
			});
		},
		handleHelp() {
			this.$tab.navigateTo('/pages/mine/help/index');
		},
		handleAbout() {
			this.$tab.navigateTo('/pages/mine/about/index');
		},
		handleJiaoLiuQun() {
			this.$modal.showToast('QQ群：①133713780(满)、②146013835(满)、③189091635');
		},
		handleBuilding() {
			this.$modal.showToast('模块建设中~');
		},
	},
};
</script>

<style lang="scss">
page {
	background-color: #f5f6f7;
}

.mine-container {
	width: 100%;
	height: 100%;

	.header-section {
		padding: 15px 15px 45px 15px;
		background-color: #3c96f3;
		color: white;

		.login-tip {
			font-size: 18px;
			margin-left: 10px;
		}

		.cu-avatar {
			border: 2px solid #eaeaea;

			.icon {
				font-size: 40px;
			}
		}

		.user-info {
			margin-left: 15px;

			.u_title {
				font-size: 18px;
				line-height: 30px;
			}
		}
	}

	.content-section {
		position: relative;
		top: -50px;

		.mine-actions {
			margin: 15px 15px;
			padding: 20px 0px;
			border-radius: 8px;
			background-color: white;

			.action-item {
				.icon {
					font-size: 28px;
				}

				.text {
					display: block;
					font-size: 13px;
					margin: 8px 0px;
				}
			}
		}
	}
}
</style>
