<template>
  <view class="container">
    <!-- 客户基础信息 -->
    <view class="block-info">
      <view class="block-info_cell">
        <text class="label">客户性别</text>
        <text class="gender">{{genderVal}}</text>

        <switch-block
          :switchList="gender.list"
          v-model="gender.index"
          :width="250"
          :height="56"
        />
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 这是2个block的案例
 * 常用语性别切换等2个block进行切换等场景
 * tips：因为不需要进行事件处理，所以不用接收事件回调
 */
import SwitchBlock from '../components/liusheng22-switchBlock/liusheng22-switchBlock.vue'

export default {
  components: {
    SwitchBlock
  },
  computed: {
    genderVal() {
      let { list, index } = this.gender
      return list[index]
    }
  },
  data() {
    return {
      gender: {
        list: ['男', '女'],
        index: 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
view {
  box-sizing: border-box;
}

@mixin font($size: 28, $color: #1D2541, $fontFamily: PingFangSC) {
  $weight: 400;
  font-size: $size + rpx;
  @if $size >= 28 {
    $weight: 500;
  }
  font-weight: $weight;
  color: $color;
  font-family: $fontFamily;
}

@mixin flex($direction: row) {
  display: flex;
  flex-direction: $direction;
}

.container {
  width: 750rpx;
  height: auto;
  min-height: 100vh;
  background-color: #F6F8F9;
  padding-bottom: 176rpx;

  .block-info {
    width: 686rpx;
    height: auto;
    background-color: #FFF;
    border-radius: 8rpx;
    padding: 32rpx 24rpx;
    margin: 0 auto;
    margin-top: 24rpx;
    &_cell {
      width: 100%;
      height: 96rpx;
      line-height: 96rpx;
      @include flex;
      align-items: center;
      flex: auto;
      box-shadow: 0 2rpx 0 0 #F5F5F5;
      .label {
        height: 40rpx;
        line-height: 40rpx;
        @include font(28, #60677C);
        font-weight: 400;
        margin-right: 40rpx;
        flex: none;
      }
      .gender {
        flex: auto;
        height: 40rpx;
        line-height: 40rpx;
        @include font(28, #60677C);
        font-weight: 400;
      }
    }
  }
}
</style>