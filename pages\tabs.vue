<template>
  <view class="container">
    <!-- tabs切换 -->
    <view class="nav-tabs">
      <switch-block
        :switchList="tabs.list"
        v-model="tabs.index"
        :width="686"
        :height="76"
        @change="switchItem"
      />
    </view>
  </view>
</template>

<script>
/**
 * 这个多个block的案例
 * 常用语tabs切换
 */
import SwitchBlock from '../components/liusheng22-switchBlock/liusheng22-switchBlock.vue'

export default {
  components: {
    SwitchBlock
  },
  data() {
    return {
      tabs: {
        list: ['待处理', '已处理', '不处理'],
        index: 0
      }
    }
  },
  methods: {
    // 切换tabs
    switchItem(item) {
      // 调用切换tabs的列表接口
    }
  }
}
</script>

<style lang="scss" scoped>
.nav-tabs {
  width: 100%;
  height: auto;
}
</style>