<template>
	<view class="th-file-box">
		<slot>
			<image class="file-image" :src="setImg(file.name)"></image>
		</slot>
		<view class="file-content-box">
			<view class="title-text text-zh" :style="{'-webkit-line-clamp':1}">{{file.name}}</view>
			<view class="desc-text">{{toMB(file.size)}}{{`&nbsp;&nbsp;&nbsp;`}}上传完成</view>
			<view class="text-priview">
				<view @tap="priview">预览</view>
			</view>

		</view>


	</view>
</template>

<script>
	export default {
		name: "th-file",
		data() {
			return {
				src: ""
			};
		},
		props: {
			file: {
				type: Object,
				default: {}
			}
		},
		methods: {
			toMB(size) {
				if (!size)
					return '未知大小'
				if (size < 1024)
					return size + 'KB'
				else
					return (size / 1024).toFixed(2) + 'M'
			},
			async priview() {
				// #ifdef H5
				this.getDownloadFileType(this.file.url)

				return

				// #endif

				// 新增APP端处理
				// #ifdef APP-PLUS
				try {
					uni.showLoading({
						title: '加载中'
					})
					const {
						tempFilePath
					} = await this.downloadFile(this.file.url)
					this.openAppDocument(tempFilePath)
				} catch (e) {
					console.error('预览失败:', e)
					uni.showToast({
						title: '预览失败',
						icon: 'none'
					})
				} finally {
					uni.hideLoading()
				}
				// #endif
			},
			// 新增APP端文档打开方法
			openAppDocument(tempFilePath) {
				const ext = this.file.name.split('.').pop().toLowerCase()
				const typeMap = {
					pdf: 'pdf',
					doc: 'doc',
					docx: 'doc',
					xls: 'xls',
					xlsx: 'xls',
					ppt: 'ppt',
					pptx: 'ppt',
					txt: 'txt'
				}

				uni.openDocument({
					filePath: tempFilePath,
					fileType: typeMap[ext] || '',
					success: () => {
						console.log('打开文档成功')
					},
					fail: (err) => {
						console.error('打开失败:', err)
						uni.showToast({
							title: '没有找到可以打开该文件的应用',
							icon: 'none'
						})
					}
				})
			},
			predownload() {

				// this.download(this.file.url)
				console.log(this.file.url, 'url')
				uni.downloadFile({
					url: this.file.url,
					success: function(res) {
						console.log(res, 'res')
						var filePath = res.tempFilePath;
						uni.openDocument({
							filePath: filePath,
							success: function(res) {
								console.log("打开文档成功");
							}
						});
					}
				});
			},
			download(path) {
				if (!path) return
				uni.showLoading({
					title: ''
				})
				let type = path.split('.')
				if (type.length > 0) {
					const image = ['bmp', 'jpg', 'jpeg', 'png', 'tif', 'gif', 'pcx', 'tga', 'exif', 'fpx', 'svg', 'psd',
						'cdr', 'pcd', 'dxf', 'ufo', 'eps', 'ai', 'raw', 'WMF', 'webp'
					]
					const video = ['avi', 'mov', 'rmvb', 'flv', 'mp4']
					const files = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf']
					if (image.indexOf(type[type.length - 1]) != -1) {
						//图片
						this.downloadFile(path, 'photo')
					} else if (video.indexOf(type[type.length - 1]) != -1) {
						//视频
						this.downloadFile(path, 'video')
					} else {
						//其它文件
						this.downloadFile(path, 'file')

					}
				} else {
					//其它文件
					this.downloadFile(path, 'file')
				}
			},

			//多媒体预览
			privewMedia(object) {
				uni.hideLoading()
				// #ifdef MP-WEIXIN
				uni.previewMedia({
					sources: [object]
				})
				// #endif
			},
			openDocument(filePath) {
				uni.hideLoading()
				uni.openDocument({
					filePath: filePath
				})

			},

			// 修改后的通用下载方法
			downloadFile(url) {
				return new Promise((resolve, reject) => {
					uni.downloadFile({
						url: url,
						success: (res) => {
							if (res.statusCode === 200) {
								resolve(res)
							} else {
								reject(new Error('下载失败'))
							}
						},
						fail: reject
					})
				})
			},

			getDownloadFileType(path) {
				// 原有H5实现保持不变
				// #ifdef H5
				if (!path) return
				uni.showLoading({
					title: ''
				})
				// #endif
				
				let type = path.split('.')
				console.log(path, 'abc')
				if (type.length > 0) {


					uni.openDocument({
						filePath: path,
						fileType: 'pdf',
						success: function() {
							console.log('文件打开成功');
							uni.hideLoading()
						},
						fail: function() {
							console.log('文件打开失败');
							uni.hideLoading()
						}
					});
					




				} else {
					//无法识别类型
					uni.hideLoading()
					this.toast('当前类型无法预览')
				}
				//
			},




			getType(file) {
				let imgType = ['bmp', 'jpg', 'jpeg', 'png', 'tif', 'gif', 'pcx', 'tga', 'exif', 'fpx', 'svg', 'psd', 'cdr',
					'pcd', 'dxf', 'ufo', 'eps', 'ai', 'raw', 'WMF', 'webp'
				]
				let fileTypev = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf']
				let videoType = ['avi', 'mov', 'rmvb', 'flv', 'mp4']
				let spertas = file.split('.')
				console.log('src:' + spertas)
				if (spertas.length > 0) {
					if (imgType.indexOf(spertas[spertas.length - 1]) != -1)
						return 'img'
					if (fileTypev.indexOf(spertas[spertas.length - 1]) != -1)
						return 'txt'
					if (videoType.indexOf(spertas[spertas.length - 1]) != -1)
						return 'vdo'
				}
				return 'unknow'
			},
			setImg(file) {
				let src = ''
				switch (this.getType(file)) {
					case 'img':
						src =
							"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoBAMAAAET63VGAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAtUExURUxpcQC4VPX7/wC6Uwe7WI3duPL3/+/3/wC4VO/2/////8Xt3h/BaWLRmXLWpRQVcsIAAAAIdFJOUwDwDiWs/X7IfhDxuAAAARdJREFUKM+FUjtOw0AQHYmGPikoXaUOXUrouAJKkyPkClFAmsUsipDyGRlSW5uWwk7EASwfBYkzMLvrcdYmlp+046c3761nVwvXCIC8YvedAeoV4MJKLwioLEG0BjymzoK+EsCNqw+Iy0C31WS2HjD0K1ufE8enc67KZ1EcR2NSSZx362DfwmKaeaYpn3sWP562MPHtBcCtZ3d19qm5nzE8buff3FBtLe2dbydMk7BCmCLpqj1FVXdXvHtWnD4qltPa3bHFsj6HYAB1U8CHBWyhkvhyGYdQ6nJl2OO6HMz6XWe8/pPe6LMtlUSrhqR/iH6jhkSMpClp2pe0jdpDlLQJpNgG16OofoE8Uz5N8i8e4uo+zA3HAH986GBIByVt5gAAAABJRU5ErkJggg=="
						break
					case 'txt':
						src =
							'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoBAMAAAET63VGAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAhUExURTKW+kxpcTaZ/DCY//H3//H3/+/2/////83m/ozE/VCm+n1OVwAAAAAGdFJOU/8ArCXGc+f13qUAAADwSURBVCjPjVJLDoJADG0wGWdcOTHu9QZ6A008gEeARBtwxW5cEheIFzB6AeMx7XwFFLFJy+P1vU6HAGMYA1By89wCiBhsDCmZw7p5zo0EbEVfZwCDGq+rKnQ9Ql3PdBWpwdVKE9ZrQlKelcotenOd6OIRx7UFAkuH+LXawCI4HJoHb9ScpxSt23maWarN5b37xR4J9OjhEUPfZVnoxixxsurgUIlZmDcI93gzEtrRTdHHpTj+oyr6Zn03Fv2qX5Sgy7aoJ2LSoMQN8d5UIUXapGgSx93HiU/c1964NmZTGf5AgFF5S8sTzZrM6r5oKeULXQ00cgxk1z8AAAAASUVORK5CYII='
						break
					case 'vdo':
						src =
							'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoBAMAAAET63VGAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAtUExURUxpcfhNEPhSFfhMDvD3//H3//dOEO/2//////rXy/Db2fOmjPZjLvSScfHDtm7LORYAAAAGdFJOUwDwrCXNftZAgEIAAAEVSURBVCjPhVIxTsNAEFwJkQcQiTppqKGhTsED8oUQaQ+SnOQiZBWZUFrnDxg3NDR0tEjhAZb8kUh5BXt3WfvsYHkkr0ezM+u9k2GAAMjPs3tPAXUEOLPSEwIqSxCtAfPEWdBXArh29QZxHui2mszWFEO/snW5dfw44ap8FsWRG5NIop7WwT6ELWjqmab3iWfq5/gA9749A7jzbFRlH5vzjOF1O7/mlmprSe9+G2GahBXCFElXxVV3U0SeFd+vJ/ZFsbtji3l1DsEQqqaADwvYwkniy2WkodTlyrDH9X8w63fVWJ1JS9q1pZIoakh6T/TbdBFj25Q0xSWtz5Yo6SWQFjYYX9Z/IO9E+7fDJ8+6GIe5q1uAP70zDXveYi4aAAAAAElFTkSuQmCC'
						break
					default:
						src =
							'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAoBAMAAAET63VGAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAqUExURUxpcfi+aPWnIvaqKPD3//H3//WmI+/2//////br1/LQlvHgwvLHfPS0SXHgbQ8AAAAGdFJOUwAZ8KzNfpy7tfkAAAESSURBVCjPhVJBSsQwFP1uXBdB9yK4FheuZ+EB5gozwosZIoyg86HVbcmshXbAA8h4ARnwHoO38Sdpalqm9kFfX99/P/kJpWMQQR7t31OCWRLmzroHQTkBuAA2pY8gMBNder4BZonv2FaO10jzyvGi8Ho3EVahFzGxsbaMHX+rDajvqDRPgzJcT4LSH7tbOgvlOdFJUFdt7113PWtl3MHd/FB9rxydbxWV4aj2USmOVZVz1lRX6jGo/ftro2rO/R07zNpzRJxTW4yQwxJ6aCy5XME6tYZSFUZShxur8dR/1oJf+tYP87JjmS3zZ9axWFB0LcO55qesv6PmZ0q/BPlp1v6BwEO9Lb7eZIij67TvQlb/BW9yDDPkSVsuAAAAAElFTkSuQmCC'
						break
				}

				return src
			},
			toast(title) {
				uni.showToast({
					title: title
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.th-file-box {
		width: 100%;
		min-height: 120rpx;
		background: #F6F7FB;
		border-radius: 8rpx;
		box-sizing: border-box;
		padding: 14rpx 24rpx;
		display: flex;
		align-items: center;
		border-radius: 8rpx;

		.file-image {
			width: 54rpx;
			height: 62rpx;
			white-space: nowrap;
		}

		.title-text {
			font-size: 30rpx;
			font-weight: 500;
			color: #333333;
			line-height: 42rpx;
			word-break: break-all;
		}

		.desc-text {
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
			line-height: 34rpx;
		}
	}

	.file-content-box {
		flex: 1;
		margin-left: 18rpx;
		position: relative;


		.text-priview {
			display: flex;
			align-items: center;
			position: absolute;
			right: 0;
			bottom: 0;
			font-size: 24rpx;
			font-family: PingFang-SC-Medium, PingFang-SC;
			font-weight: 500;
			color: #0077FF;
			line-height: 34rpx;

			.left36 {
				margin-left: 36rpx;
			}
		}
	}
</style>