<template>
	<view>
		<view class="p-3 rounded-lg bg-white mt-2" v-for="(item,index) in loadList" :key="index">
			<view class="" @click="toVideo(id[index])">
				<text class="font32 line15 m-3" style="font-weight: 900;">{{item.name}}</text>
				<view class="mt-2 position-relative">
					<view class="position-absolute left-0 right-0 top-0 bottom-0 flex align-center justify-center"
						style="z-index: 9;position: ;">
						<uni-icons type="videocam" size="40" color="#ffffff"></uni-icons>
					</view>
					<image :src="item.thumb" style="width:650rpx;height: 350rpx;" mode="aspectFit"></image>
				</view>
				<view class="flex align-center justify-between mt-1">
					<view class="flex align-center">
						<uni-icons type="eye" size="16" color="#cccccc"></uni-icons>
						<text class="font24 text-muted">时长：{{item.duration}} 秒</text>
					</view>
					<view class="flex align-center">
						<uni-icons type="calendar" size="16" color="#cccccc"></uni-icons>
						<text class="font24 text-muted">格式：{{item.type}}</text>
					</view>
				</view>
			</view>
		</view>
		<o-empty v-show="loadList<=0" />
	</view>
</template>

<script>
	import request from '@/utils/request'
	export default {
		name: "i-video-list",
		props: {

			id: {
				type: Number,
				require: true
			}
		},
		data() {
			return {
				loadList: [],

			};
		},
		mounted() {
			this.loadData();
		},
		watch: {
			id: {
				deep: true,
				handler() {
					this.loadData();
				}
			}
		},
		methods: {
			async loadData() {
				try {
					// 按顺序创建promise数组
					const promises = this.$props.id.map(i => this.getVideoList(i));
					// 并行执行但按顺序等待结果
					const results = await Promise.all(promises);

					// 过滤无效结果并保持原始顺序
					this.loadList = results.filter(item => !!item);
				} catch (error) {
					console.error('加载失败:', error);
				}
			},
			toVideo(i) {
				uni.navigateTo({
					url: `/pages/studyVideo/video/index?id=${i}`,
					fail(res) {
						console.log(res)
					}
				})
			},
			//获取视频详情
			async getVideoList(i) {
				try {
					const response = await request({
						url: '/ApiCaCourse/GetById',
						method: 'POST',
						data: {
							id: i
						},
					});
					return response?.videoFile || null;
				} catch (error) {
					console.error('请求失败:', error);
					return null;
				}
			}
		}
	}
</script>

<style>
	/* #ifndef APP-PLUS-NVUE */
	.status_bar {
		height: var(--status-bar-height);
		width: 100%;
	}

	/* scroll-view */
	.scroll-row {
		width: 100%;
		white-space: nowrap;
	}

	.scroll-row-item {
		display: inline-block !important;
	}

	/* #endif */
	/* 图标 */
	.iconfont {
		font-family: iconfont;
	}

	.view,
	.text {
		font-size: 28rpx;
		line-height: 1.8;
		color: #000000;
	}

	.img-in {
		font-size: 0;
	}

	/* 宽度 */
	/* #ifndef APP-PLUS-NVUE */
	.w-100 {
		width: 100%;
	}

	.w-80 {
		width: 80%;
	}

	.w-33 {
		width: 33%;
	}

	/* #endif */
	.w-50 {
		width: 50%;
	}

	.w-25 {
		width: 25%;
	}

	.w-20 {
		width: 20%;
	}

	.row {
		margin-right: -20rpx;
		margin-left: -20rpx;
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		/* #endif */
		flex-wrap: wrap;
		flex-direction: row;
	}


	.col-12 {
		width: 750rpx;
	}

	.col-11 {
		width: 687.5rpx;
	}

	.col-10 {
		width: 625rpx;
	}

	.col-9 {
		width: 562.5rpx;
	}

	.col-8 {
		width: 500rpx;
	}

	.col-7 {
		width: 437.5rpx;
	}

	.col-6 {
		width: 375rpx;
	}

	.col-5 {
		width: 312.5rpx;
	}

	.col-4 {
		width: 250rpx;
	}

	.col-3 {
		width: 187.5rpx;
	}

	.col-2 {
		width: 125rpx;
	}


	.col-1 {
		width: 62.5rpx;
	}

	.col-offset-12 {
		margin-left: 750rpx;
	}

	.col-offset-11 {
		margin-left: 687.5rpx;
	}

	.col-offset-10 {
		margin-left: 625rpx;
	}

	.col-offset-9 {
		margin-left: 562.5rpx;
	}

	.col-offset-8 {
		margin-left: 500rpx;
	}

	.col-offset-7 {
		margin-left: 437.5rpx;
	}

	.col-offset-6 {
		margin-left: 375rpx;
	}

	.col-offset-5 {
		margin-left: 312.5rpx;
	}

	.col-offset-4 {
		margin-left: 250rpx;
	}

	.col-offset-3 {
		margin-left: 187.5rpx;
	}

	.col-offset-2 {
		margin-left: 125rpx;
	}

	.col-offset-1 {
		margin-left: 62.5rpx;
	}

	.col-offset-0 {
		margin-left: 0;
	}

	/* flex 布局 */
	.flex {
		/* #ifndef APP-PLUS-NVUE */
		display: flex !important;
		/* #endif */
		flex-direction: row;
	}

	.flex-row {
		flex-direction: row !important;
	}

	.flex-column {
		flex-direction: column !important;
	}

	.flex-row-reverse {
		flex-direction: row-reverse !important;
	}

	.flex-column-reverse {
		flex-direction: column-reverse !important;
	}

	.flex-wrap {
		flex-wrap: wrap;
	}

	.flex-nowrap {
		flex-wrap: nowrap;
	}

	.justify-start {
		justify-content: flex-start;
	}

	.justify-end {
		justify-content: flex-end;
	}

	.justify-between {
		justify-content: space-between;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.align-stretch {
		align-items: stretch;
	}

	.align-start {
		align-items: flex-start;
	}

	.align-end {
		align-items: flex-end;
	}

	/* #ifndef APP-PLUS-NVUE */
	.content-start {
		align-content: flex-start;
	}

	.content-end {
		align-content: flex-end;
	}

	.content-center {
		align-content: center;
	}

	.content-between {
		align-content: space-between;
	}

	.content-around {
		align-content: space-around;
	}

	.content-stretch {
		align-content: stretch;
	}

	/* #endif */
	.flex-1 {
		flex: 1;
	}

	.flex-2 {
		flex: 2;
	}

	.flex-3 {
		flex: 3;
	}

	.flex-4 {
		flex: 4;
	}

	.flex-5 {
		flex: 5;
	}

	/* #ifndef APP-PLUS-NVUE */
	.flex-shrink {
		flex-shrink: 0;
	}

	/* #endif */

	.containerx {
		padding-right: 20rpx;
		padding-left: 20rpx;
	}

	/*  -- 内外边距 -- */
	.m-0 {
		margin: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.m-auto {
		margin: auto;
	}

	/* #endif */
	.m-1 {
		margin: 10rpx;
	}

	.m-2 {
		margin: 20rpx;
	}

	.m-3 {
		margin: 30rpx;
	}

	.m-4 {
		margin: 40rpx;
	}

	.m-5 {
		margin: 50rpx;
	}

	.mt-0 {
		margin-top: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.mt-auto {
		margin-top: auto;
	}

	/* #endif */
	.mt-1 {
		margin-top: 10rpx;
	}

	.mt-2 {
		margin-top: 20rpx;
	}

	.mt-3 {
		margin-top: 30rpx;
	}

	.mt-4 {
		margin-top: 40rpx;
	}

	.mt-5 {
		margin-top: 50rpx;
	}

	.mb-0 {
		margin-bottom: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.mb-auto {
		margin-bottom: auto;
	}

	/* #endif */
	.mb-1 {
		margin-bottom: 10rpx;
	}

	.mb-2 {
		margin-bottom: 20rpx;
	}

	.mb-3 {
		margin-bottom: 30rpx;
	}

	.mb-4 {
		margin-bottom: 40rpx;
	}

	.mb-5 {
		margin-bottom: 50rpx;
	}

	.ml-0 {
		margin-left: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.ml-auto {
		margin-left: auto;
	}

	/* #endif */
	.ml-1 {
		margin-left: 10rpx;
	}

	.ml-2 {
		margin-left: 20rpx;
	}

	.ml-3 {
		margin-left: 30rpx;
	}

	.ml-4 {
		margin-left: 40rpx;
	}

	.ml-5 {
		margin-left: 50rpx;
	}

	.mr-0 {
		margin-right: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.mr-auto {
		margin-right: auto;
	}

	/* #endif */
	.mr-1 {
		margin-right: 10rpx;
	}

	.mr-2 {
		margin-right: 20rpx;
	}

	.mr-3 {
		margin-right: 30rpx;
	}

	.mr-4 {
		margin-right: 40rpx;
	}

	.mr-5 {
		margin-right: 50rpx;
	}

	.my-0 {
		margin-top: 0;
		margin-bottom: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.my-auto {
		margin-top: auto;
		margin-bottom: auto;
	}

	/* #endif */
	.my-1 {
		margin-top: 10rpx;
		margin-bottom: 10rpx;
	}

	.my-2 {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.my-3 {
		margin-top: 30rpx;
		margin-bottom: 30rpx;
	}

	.my-4 {
		margin-top: 40rpx;
		margin-bottom: 40rpx;
	}

	.my-5 {
		margin-top: 50rpx;
		margin-bottom: 50rpx;
	}

	.mx-0 {
		margin-left: 0;
		margin-right: 0;
	}

	/* #ifndef APP-PLUS-NVUE */
	.mx-auto {
		margin-left: auto;
		margin-right: auto;
	}

	/* #endif */
	.mx-1 {
		margin-left: 10rpx;
		margin-right: 10rpx;
	}

	.mx-2 {
		margin-left: 20rpx;
		margin-right: 20rpx;
	}

	.mx-3 {
		margin-left: 30rpx;
		margin-right: 30rpx;
	}

	.mx-4 {
		margin-left: 40rpx;
		margin-right: 40rpx;
	}

	.mx-5 {
		margin-left: 50rpx;
		margin-right: 50rpx;
	}

	.p-0 {
		padding: 0;
	}

	.p {
		padding: 5rpx;
	}

	.p-1 {
		padding: 10rpx;
	}

	.p-2 {
		padding: 20rpx;
	}

	.p-3 {
		padding: 30rpx;
	}

	.p-4 {
		padding: 40rpx;
	}

	.p-5 {
		padding: 50rpx;
	}

	.pt-0 {
		padding-top: 0;
	}

	.pt {
		padding-top: 5rpx;
	}

	.pt-1 {
		padding-top: 10rpx;
	}

	.pt-2 {
		padding-top: 20rpx;
	}

	.pt-3 {
		padding-top: 30rpx;
	}

	.pt-4 {
		padding-top: 40rpx;
	}

	.pt-5 {
		padding-top: 50rpx;
	}

	.pb-0 {
		padding-bottom: 0;
	}

	.pb-1 {
		padding-bottom: 10rpx;
	}

	.pb {
		padding-bottom: 5rpx;
	}

	.pb-2 {
		padding-bottom: 20rpx;
	}

	.pb-3 {
		padding-bottom: 30rpx;
	}

	.pb-4 {
		padding-bottom: 40rpx;
	}

	.pb-5 {
		padding-bottom: 50rpx;
	}

	.pl-0 {
		padding-left: 0;
	}

	.pl {
		padding-left: 5rpx;
	}

	.pl-1 {
		padding-left: 10rpx;
	}

	.pl-2 {
		padding-left: 20rpx;
	}

	.pl-3 {
		padding-left: 30rpx;
	}

	.pl-4 {
		padding-left: 40rpx;
	}

	.pl-5 {
		padding-left: 50rpx;
	}

	.pl-9 {
		padding-left: 90rpx;
	}

	.pr-0 {
		padding-right: 0;
	}

	.pr {
		padding-right: 5rpx;
	}

	.pr-1 {
		padding-right: 10rpx;
	}

	.pr-2 {
		padding-right: 20rpx;
	}

	.pr-3 {
		padding-right: 30rpx;
	}

	.pr-4 {
		padding-right: 40rpx;
	}

	.pr-5 {
		padding-right: 50rpx;
	}

	.py-0 {
		padding-top: 0;
		padding-bottom: 0;
	}

	.py {
		padding-top: 5rpx;
		padding-bottom: 5rpx;
	}

	.py-1 {
		padding-top: 10rpx;
		padding-bottom: 10rpx;
	}

	.py-2 {
		padding-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.py-25 {
		padding-top: 25rpx;
		padding-bottom: 25rpx;
	}

	.py-3 {
		padding-top: 30rpx;
		padding-bottom: 30rpx;
	}

	.py-4 {
		padding-top: 40rpx;
		padding-bottom: 40rpx;
	}

	.py-5 {
		padding-top: 50rpx;
		padding-bottom: 50rpx;
	}

	.px-0 {
		padding-left: 0;
		padding-right: 0;
	}

	.px-1 {
		padding-left: 10rpx;
		padding-right: 10rpx;
	}

	.px {
		padding-left: 5rpx;
		padding-right: 5rpx;
	}

	.px-2 {
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.px-3 {
		padding-left: 30rpx;
		padding-right: 30rpx;
	}

	.px-4 {
		padding-left: 40rpx;
		padding-right: 40rpx;
	}

	.px-5 {
		padding-left: 50rpx;
		padding-right: 50rpx;
	}

	.font16 {
		font-size: 16rpx !important;
	}

	.font18 {
		font-size: 18rpx !important;
	}

	.font20 {
		font-size: 20rpx !important;
	}

	.font22 {
		font-size: 22rpx !important;
	}

	.font24 {
		font-size: 24rpx !important;
	}

	.font26 {
		font-size: 26rpx !important;
	}

	.font28 {
		font-size: 28rpx !important;
	}

	.font30 {
		font-size: 30rpx !important;
	}

	.font32 {
		font-size: 32rpx !important;
	}

	.font34 {
		font-size: 34rpx !important;
	}

	.font36 {
		font-size: 36rpx !important;
	}

	.font38 {
		font-size: 38rpx !important;
	}

	.font40 {
		font-size: 40rpx !important;
	}

	.font50 {
		font-size: 50rpx !important;
	}

	.font60 {
		font-size: 60rpx !important;
	}

	/* 文字缩进 */
	/* #ifndef APP-PLUS-NVUE */
	.text-indent {
		text-indent: 2;
	}

	.line15 {
		line-height: 1.5;
	}

	.line2 {
		line-height: 1.8;
	}

	/* #endif */
	/* 文字划线 */
	.text-through {
		text-decoration: line-through;
	}

	/* 文字对齐 */
	.text-left {
		text-align: left;
	}

	.text-right {
		text-align: right;
	}

	.text-center {
		text-align: center;
	}

	/* 文字换行溢出处理 */
	.text-ellipsis {
		/* #ifndef APP-PLUS-NVUE */
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		/* #endif */
		/* #ifdef APP-PLUS-NVUE */
		lines: 1;
		/* #endif */
	}

	.text-ellipsis-1 {
		/* #ifndef APP-PLUS-NVUE */
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		line-clamp: 1;
		-webkit-box-orient: vertical;
		/* #endif */
	}


	.text-ellipsis-2 {
		/* #ifndef APP-PLUS-NVUE */
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		/* #endif */
	}

	/* 文字粗细和斜体 */
	.font-weight-light {
		font-weight: 300;
	}

	/*细*/
	.font-weight-lighter {
		font-weight: 100;
	}

	/*更细*/
	.font-weight-normal {
		font-weight: 400;
	}

	/*正常*/
	.font-weight-bold {
		font-weight: 700;
	}

	/*粗*/
	.font-weight-bolder {
		font-weight: bold;
	}

	/*更粗*/
	.font-italic {
		font-style: italic;
	}

	/*斜体*/
	/* 文字颜色 */
	.text-white {
		color: #ffffff;
	}

	.text-primary {
		color: #2979ff;
	}

	.text-hover-primary {
		color: #94bcff;
	}

	.text-secondary {
		color: #6c757d;
	}

	.text-hover-secondary {
		color: #494f54;
	}

	.text-success {
		color: #18bc37;
	}

	.text-hover-success {
		color: #8cde9b;
	}

	.text-info {
		color: #8f939c;
	}

	.text-hover-info {
		color: #c7c9ce;
	}

	.text-warning {
		color: #ffc107;
	}

	.text-hover-warning {
		color: #f9d39f;
	}

	.text-danger {
		color: #e43d33;
	}

	.text-hover-danger {
		color: #f29e99;
	}

	.text-light {
		color: #e9e9eb;
	}

	.text-hover-light {
		color: #cbd3da;
	}

	.text-dark {
		color: #343a40;
	}

	.text-hover-dark {
		color: #8f939c;
	}

	.text-body {
		color: #000000;
	}

	.text-muted {
		color: #979797;
	}

	/* 浅灰色 */
	.text-light-muted {
		color: #A9A5A0;
	}

	.text-light-black {
		color: rgba(0, 0, 0, 0.5);
	}

	.text-light-white {
		color: rgba(255, 255, 255, 0.5);
	}

	/* 背景颜色 */

	.bg-primary {
		background-color: #2979ff;
	}

	.bg-hover-primary:hover {
		background-color: #94bcff;
	}

	.bg-secondary {
		background-color: #6c757d;
	}

	.bg-hover-secondary:hover {
		background-color: #545b62;
	}

	.bg-success {
		background-color: #18bc37;
	}

	.bg-hover-success {
		background-color: #8cde9b;
	}

	.bg-info {
		background-color: #8f939c;
	}

	.bg-hover-info {
		background-color: #c7c9ce;
	}

	.bg-warning {
		background-color: #f3a73f;
	}

	.bg-hover-warning {
		background-color: #f9d39f;
	}

	.bg-danger {
		background-color: #e43d33;
	}

	.bg-hover-danger {
		background-color: #f29e99;
	}

	.bg-light {
		background-color: #e9e9eb;
	}

	.bg-hover-light {
		background-color: #f1f1f1;
	}

	.bg-dark {
		background-color: #333333;
	}

	.bg-hover-dark {
		background-color: #1d2124;
	}

	.bg-white {
		background-color: #ffffff;
	}

	.bg-transparent {
		background-color: transparent;
	}

	/* 边框 */
	.border {
		border-width: 1rpx;
		border-style: solid;
		border-color: #dee2e6;
	}

	.border-top {
		border-top-width: 1rpx;
		border-top-style: solid;
		border-top-color: #dee2e6;
	}

	.border-right {
		border-right-width: 1rpx;
		border-right-style: solid;
		border-right-color: #dee2e6;
	}

	.border-bottom {
		border-bottom-width: 1rpx;
		border-bottom-style: solid;
		border-bottom-color: #dee2e6;
	}

	.border-left {
		border-left-width: 1rpx;
		border-left-style: solid;
		border-left-color: #dee2e6;
	}

	.border-dotted-bottom {
		border-bottom-width: 1rpx;
		border-bottom-style: dotted;
		border-bottom-color: #dee2e6;
	}

	.border-0 {
		border-width: 0 !important;
	}

	.border-top-0 {
		border-top-width: 0 !important;
	}

	.border-right-0 {
		border-right-width: 0 !important;
	}

	.border-bottom-0 {
		border-bottom-width: 0 !important;
	}

	.border-left-0 {
		border-left-width: 0 !important;
	}

	.border-primary {
		border-color: #2979ff;
	}

	.border-secondary {
		border-color: #6c757d;
	}

	.border-light-secondary {
		border-color: #E9E8E5;
	}

	.border-success {
		border-color: #18bc37;
	}

	.border-info {
		border-color: #8f939c;
	}

	.border-warning {
		border-color: #f3a73f;
	}

	.border-danger {
		border-color: #e43d33;
	}

	.border-light {
		border-color: #e9e9eb;
	}

	.border-dark {
		border-color: #343a40;
	}

	.border-white {
		border-color: #FFFFFF;
	}


	/* 圆角 */
	.rounded {
		border-radius: 8rpx;
	}

	.rounded-lg {
		border-radius: 14rpx;
	}

	.rounded-top {
		border-top-left-radius: 8rpx;
		border-top-right-radius: 8rpx;
	}

	.rounded-top-lg {
		border-top-left-radius: 14rpx;
		border-top-right-radius: 14rpx;
	}

	.rounded-right {
		border-top-right-radius: 8rpx;
		border-bottom-right-radius: 8rpx;
	}

	.rounded-bottom {
		border-bottom-right-radius: 8rpx;
		border-bottom-left-radius: 8rpx;
	}

	.rounded-bottom-lg {
		border-bottom-right-radius: 14rpx;
		border-bottom-left-radius: 14rpx;
	}

	.rounded-left {
		border-top-left-radius: 8rpx;
		border-bottom-left-radius: 8rpx;
	}

	.rounded-circle {
		border-radius: 100rpx;
	}

	.rounded-circle-left {
		border-radius: 100rpx 0 0 100rpx;
	}

	.rounded-circle-right {
		border-radius: 0 100rpx 100rpx 0;
	}

	.rounded-0 {
		border-radius: 0;
	}

	/* 显示 */
	/* #ifndef APP-PLUS-NVUE */
	.d-none {
		display: none;
	}

	.d-inline-block {
		display: inline-block;
	}

	.d-block {
		display: block;
	}

	/* #endif */
	/* 内容溢出 */
	.overflow-hidden {
		overflow: hidden;
	}

	/* 定位 */
	.position-relative {
		position: relative;
	}

	.position-absolute {
		position: absolute;
	}

	.position-fixed {
		position: fixed;
	}

	.position-sticky {
		position: sticky;
		top: 0;
		z-index: 1000;
	}

	/* 定位 - 固定顶部 */
	.fixed-top {
		position: fixed;
		top: 0;
		right: 0;
		left: 0;
		z-index: 1030;
	}

	/* 定位 - 固定底部 */
	.fixed-bottom {
		position: fixed;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 3;
	}

	.top-0 {
		top: 0;
	}

	.left-0 {
		left: 0;
	}

	.right-0 {
		right: 0;
	}

	.bottom-0 {
		bottom: 0;
	}

	/* 阴影 */
	/* #ifndef APP-PLUS-NVUE */
	.shadow {
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
	}

	.shadow-lg {
		box-shadow: 0rpx 40rpx 100rpx 0rpx rgba(0, 0, 0, 0.175);
	}

	.shadow-none {
		box-shadow: none !important;
	}

	/* #endif */
	.cu-tabbar-height {
		height: 110upx;
	}

	.cny::before {
		content: "¥";
		font-size: 60%;
		margin-right: 10rpx;
	}

	.opacity9 {
		opacity: 0.9;
	}
</style>