<style>
	.container {
		width: 100vw;
		height: 100vh;
	}

	.video {
		width: 100vw;
		height: 30vh;
		position: relative;
		/* 添加相对定位 */
	}

	video {
		width: 100vw;
		height: 30vh;
	}

	.loading-mask {
		position: absolute;
		/* 改为绝对定位 */
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		/* 添加半透明背景 */
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		/* 文字颜色改为白色 */
	}

	/* 新增播放提示样式 */
	.play-prompt {
		position: absolute;
		bottom: 20rpx;
		left: 50%;
		transform: translateX(-50%);
		background: rgba(0, 0, 0, 0.7);
		color: #fff;
		padding: 10rpx 20rpx;
		border-radius: 40rpx;
		font-size: 26rpx;
		animation: fadeIn 0.5s;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}

		to {
			opacity: 1;
		}
	}
</style>

<template>
	<view class="container">
		<view class="video">
			<sunny-video id="myVideo" ref="videoPlayer" :src="videoSrc" :poster="videoPoster" preload="auto"
				enable-storage="true" enable-stats="false" :show-mute-btn="show_mute_btn"
				:enable-play-gesture="enable_play_gesture" :show-center-play-btn="show_center_play_btn"
				:page-gesture="page_gesture" controls :show-loading="true" :auto-pause-if-navigate="true"
				@play="handleVideoPlay" @loadedmetadata="handleVideoLoaded" @error="handleVideoError"
				@controlstoggle="handleControlsToggle" @ready="onVideoReady">


				<view class="loading-mask" v-if="!videoReady && !loadingError">
					<uni-load-more status="loading" color="#FFF"
						:contentText="{contentdown: '正在加载视频资源'}"></uni-load-more>
				</view>
				<view class="loading-mask" v-if="loadingError">
					<text>视频加载失败，请检查网络后重试</text>
				</view>

				<!-- 新增播放提示 -->
				<view class="play-prompt" v-if="showPlayPrompt">
					<text>视频已就绪，点击播放</text>
				</view>


			</sunny-video>
		</view>

		<view class="content">
			<view style="margin-top:14px; margin-left: 8px; margin-right: 10px;">
				<chenchuang-tabs spaceLeft="12" v-model="industryTabIndex" :tabs="industryTabs"
					@change="tabChange"></chenchuang-tabs>
					
				<cc-listView :productList="projectList" @click="goProDetail" v-show="industryTabIndex==0">
				</cc-listView>

				<th-file :file="file1" v-show="industryTabIndex==1">
				</th-file>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request'

	const VIDEO_CACHE_EXPIRES = 3600 * 24 // 24小时缓存

	export default {
		data() {
			return {
				page_gesture: true,
				show_center_play_btn: false,
				enable_play_gesture: true,
				show_mute_btn: true,
				loadList: {},
				projectList: [],
				file1: {},
				industryTabs: [{
						name: '课程章节'
					},
					{
						name: '课程附件'
					}
				],
				industryTabIndex: 0,
				videoReady: false,
				loadingError: false,
				currentVideoId: null,
				showPlayPrompt: false,
				canAutoPlay: true,
				// 新增倍速相关数据
				speedRate: 1.0,
				showSpeedPanel: false,
				speedOptions: [0.5, 0.8, 1.0, 1.25, 1.5],
				controlsVisible: true,
				videoContext: null
			}
		},

		computed: {
			videoSrc() {
				return this.loadList.videoFile ? this.loadList.videoFile.src : ''
			},
			videoPoster() {
				return this.loadList.videoFile && this.loadList.videoFile.thumb ?
					this.loadList.videoFile.thumb :
					'/static/poster-default.jpg'
			}
		},

		onLoad(option) {
			this.currentVideoId = option.id
			this.initWithCache(option.id)
			this.getVideoList(option.id)
		},
		mounted() {
			this.videoContext = uni.createVideoContext('myVideo', this)
		},
		methods: {
			async handleVideoLoaded() {
				try {
					if (this.canAutoPlay) {
						await this.$refs.videoPlayer.play()
						this.videoReady = true
					}
				} catch (e) {
					console.log('自动播放失败，转为手动播放', e)
					this.canAutoPlay = false
					this.showPlayPrompt = true
					setTimeout(() => this.showPlayPrompt = false, 3000)
				}
			},
			// 视频播放时处理
			handleVideoPlay() {
				this.videoReady = true
				this.showPlayPrompt = false
			},

			// 视频加载错误处理
			handleVideoError(e) {
				console.error('视频加载错误:', e)
				this.loadingError = true
			},

			// 修改成功响应处理
			handleSuccessResponse(response, videoId) {

				this.canAutoPlay = true
				this.showPlayPrompt = false
			},
			initWithCache(videoId) {
				const cacheKey = `video_${videoId}`
				const cacheData = uni.getStorageSync(cacheKey)

				if (cacheData && this.isCacheValid(cacheData)) {
					this.loadList = cacheData
					this.initData(cacheData)
					this.preloadResources(cacheData)
				}
			},

			isCacheValid(cacheData) {
				return cacheData.timestamp &&
					(Date.now() - cacheData.timestamp) < VIDEO_CACHE_EXPIRES * 1000
			},

			async getVideoList(videoId) {
				try {
					const response = await request({
						url: '/ApiCaCourse/GetById',
						method: 'POST',
						data: {
							id: videoId
						},
						timeout: 8000
					})

					if (response && response.videoFile) {
						this.handleSuccessResponse(response, videoId)
					} else {
						this.handleEmptyResponse()
					}
				} catch (error) {
					this.handleRequestError(error)
				}
			},

			handleSuccessResponse(response, videoId) {
				const cacheData = {
					...response,
					timestamp: Date.now()
				}

				uni.setStorage({
					key: `video_${videoId}`,
					data: cacheData,
					expires: VIDEO_CACHE_EXPIRES
				})

				if (!this.videoReady) {
					this.loadList = response
					this.initData(response)
				}

				this.preloadResources(response)
				this.videoReady = true
			},

			handleEmptyResponse() {
				if (!this.loadList.videoFile) {
					this.loadingError = true
					uni.showToast({
						title: '课程内容不存在',
						icon: 'none'
					})
				}
			},

			handleRequestError(error) {
				console.error('请求失败:', error)
				if (!this.videoReady) {
					this.loadingError = true
					uni.showToast({
						title: '加载失败，请检查网络',
						icon: 'none'
					})
				}
			},

			initData(data) {
				this.projectList = [{
					proName: data.courseName,
					proUnit: data.courseDetails,
					area: data.courseTypePName,
					proType: data.courseTypeName,
					stage: data.tags,
				}]

				if (data.handoutFile) {
					this.file1 = {
						name: `${data.handoutFile.name}${data.handoutFileType}`,
						size: data.handoutFile.size,
						url: data.handoutFile.src
					}
				}
			},

			preloadResources(data) {
				const preloadUrls = []

				if (data.handoutFile && data.handoutFile.src) {
					preloadUrls.push(data.handoutFile.src)
				}

				if (data.videoFile && data.videoFile.src) {
					preloadUrls.push(data.videoFile.src)
				}

				if (preloadUrls.length > 0) {
					uni.preDownload({
						urls: preloadUrls,
						success: () => console.log('资源预加载成功'),
						fail: (err) => console.log('预加载失败', err)
					})
				}
			},

			// 视频准备就绪回调
			onVideoReady() {
				// 初始化播放速率
				this.videoContext.playbackRate(this.speedRate)
			},

			// 控制栏显示/隐藏
			handleControlsToggle(e) {
				this.controlsVisible = e.detail.show
			},

			// 切换倍速面板
			toggleSpeedPanel() {
				this.showSpeedPanel = !this.showSpeedPanel
			},

			// 关闭倍速面板
			closeSpeedPanel() {
				this.showSpeedPanel = false
			},

			// 设置播放速率
			setPlaybackRate(rate) {
				this.speedRate = rate
				this.videoContext.playbackRate(rate)
				this.closeSpeedPanel()

				// 兼容H5平台
				// #ifdef H5
				const videoElement = document.getElementById('myVideo')
				if (videoElement) {
					videoElement.playbackRate = rate
				}
				// #endif
			},

			handleVideoPlay() {
				this.videoReady = true
				this.showPlayPrompt = false
				// 确保倍速设置生效
				this.videoContext.playbackRate(this.speedRate)
			},
			
			tabChange() {
				console.log('切换 =' + this.industryTabIndex)
			},
			
			goProDetail() {
				// 跳转逻辑
			}
		}
	}
</script>