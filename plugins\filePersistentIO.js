// 数据持久化存储
// let addr = "file://storage/emulated/0/京唐港app/";
// let addr = "file:///storage/emulated/0/";
let addr = "JTGUser/Info/";  // 手机存储的文件位置
 
export default {
    storage(className, data) {
        plus.io.requestFileSystem(
            // plus.io.PRIVATE_DOC, // 程序私有文档目录常量
            // plus.io.PUBLIC_DOWNLOADS, // 程序私有文档目录常量
         plus.io.PUBLIC_DOCUMENTS,
            fs => {
                // 创建或打开文件, fs.root是根目录操作对象,直接fs表示当前操作对象
                fs.root.getFile(
                    addr + className,
                    {create: true},// 文件不存在则创建
                    fileEntry => {
                        // 文件在手机中的路径
                        fileEntry.createWriter(writer => {
                            // 写入文件成功完成的回调函数
                            writer.onwrite = e => {
                                //console.log('写入成功');
                            };
                            // 向文件中写入数据
                            writer.write(
                                JSON.stringify(data)
                            );
                            // 写入完成回调
                            writer.onwriteend = function(res) {
                                console.log('写入文件成功', res.target.fileName);
                            };
                            // 写入错误回调
                            writer.onerror = function(err) {
                                console.error('写入文件失败', err);
                            };
                        });
                    },
                    e => {
                        console.log('getFile failed: ', e);
                    }
                );
            },
            e => {
                console.log(e.message);
            }
        );
    },
    read(className) {
        let that = this;
        return new Promise((resolve, reject) => {
            plus.io.requestFileSystem(
                // plus.io.PRIVATE_DOC,
                // plus.io.PUBLIC_DOWNLOADS,
            plus.io.PUBLIC_DOCUMENTS,
 
                fs => {
                    fs.root.getFile(
                        addr + className,
                        {create: false},
                        fileEntry => {
                            fileEntry.file(function(file) {
                                console.log('文件大小:' + file.size + '-- 文件名:' + file.name);
                                //创建读取文件对象
                                let fileReader = new plus.io.FileReader();
                                //以文本格式读取文件数据内容
                                fileReader.readAsText(file, 'utf-8');
                                //文件读取操作完成时的回调函数
                                fileReader.onloadend = (evt) => {
                           console.log(evt.target.result)
                           console.log('evt.target.result')
                                    console.log(JSON.parse(evt.target.result),
                                        'JSON.parse(evt.target.result)')
                                    resolve(JSON.parse(evt.target.result))
                                    // sURL = JSON.parse(evt.target.result).URL;
                                }
 
                                // fileReader.onloadend = function(evt) {
                                //     resolve(evt.target.result)
                                // };
                            });
                        },
                        e => {
                            reject(e);
                        }
                    );
                },
                e => {
                    reject(e);
                    console.log(e.message);
                }
            );
        })
    }
}