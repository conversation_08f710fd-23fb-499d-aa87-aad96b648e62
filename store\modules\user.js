import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import {
	login,
	logout
} from '@/api/login'
import {
	getUserInfo
} from '@/api/system/user'


import {
	getToken,
	setToken,
	removeToken
} from '@/utils/auth'

const user = {
	state: {
		token: getToken(),
		name: storage.get(constant.name),
		avatar: storage.get(constant.avatar),
		roles: storage.get(constant.roles),
		permissions: storage.get(constant.permissions)
	},

	mutations: {
		SET_TOKEN: (state, token) => {
			state.token = token
		},
		SET_NAME: (state, name) => {
			state.name = name
			storage.set(constant.name, name)
		},
		SET_AVATAR: (state, avatar) => {
			state.avatar = avatar
			storage.set(constant.avatar, avatar)
		},
		SET_ROLES: (state, roles) => {
			state.roles = roles
			storage.set(constant.roles, roles)
		},
		SET_PERMISSIONS: (state, permissions) => {
			state.permissions = permissions
			storage.set(constant.permissions, permissions)
		}
	},

	actions: {
		// 登录
		Login({
			commit
		}, userInfo) {
			console.log('登录参数', userInfo)
			const userName = userInfo.userName.trim()
			const password = userInfo.password
			const isAutoLogon = userInfo.isAutoLogon
			return new Promise((resolve, reject) => {
				login(userName, password, isAutoLogon).then(res => {
					console.log('登录结果', res)
					setToken(res)
					commit('SET_TOKEN', res)
					resolve()
				}).catch(error => {
					reject(error)
				})
			})
		},

		// 获取用户信息
		GetInfo({
			commit
		}) {
			return new Promise((resolve, reject) => {
				getUserInfo().then(res => {
					const avatar = (res == null || res.avatar == "" || res.avatar == null) ?
						require("@/static/images/profile.jpg") : res.avatar
					const userName = (res == null || res.userName == "" || res.userName ==
						null) ? "" : res.userName
					if (res.userType && res.userType.length > 0) {
						commit('SET_ROLES', res.userType)
						commit('SET_PERMISSIONS', '*:*:*')
					} else {
						commit('SET_ROLES', ['ROLE_DEFAULT'])
					}
					commit('SET_NAME', userName)
					commit('SET_AVATAR', avatar)
					resolve(res)
				}).catch(error => {
					reject(error)
				})
			})
		},

		// 退出系统
		LogOut({
			commit,
			state
		}) {
			return new Promise((resolve, reject) => {
				logout(state.token).then(() => {
					commit('SET_TOKEN', '')
					commit('SET_ROLES', [])
					commit('SET_PERMISSIONS', [])
					removeToken()
					storage.clean()
					resolve()
				}).catch(error => {
					reject(error)
				})
			})
		}
	}
}

export default user