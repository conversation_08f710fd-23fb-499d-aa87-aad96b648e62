<script>
import config from './config';
import store from '@/store';
import { getToken } from '@/utils/auth';

export default {
	onLaunch: function () {
		// // #ifdef APP-PLUS
		// //app关闭默认的启动 方法关闭启动图。但是这个时间不能太晚，6s 超时后依旧会主动关闭。
		// setTimeout(() => {
		// 	plus.navigator.closeSplashscreen();
		// }, 3000);
		// // #endif

		this.initApp();
	},
	methods: {
		// 初始化应用
		initApp() {
			console.log('初始化应用');
			// 初始化应用配置
			this.initConfig();
			// 检查用户登录状态
			this.checkLogin();
		},
		initConfig() {
			this.globalData.config = config;
		},
		checkLogin() {
			console.log('检查用户登录状态');
			var token = getToken();

			console.log('缓存Token' + token);
			//if (!token) token = this.getTokenForFile(); //同步获取本地数据(更新安装包后，缓存删除，从文件读取)

			if (!token) {
				console.log('无缓存Token跳转登录页');
				this.$tab.reLaunch('/pages/login');
			} else {
				console.log('有缓存Token跳转首页');
				this.$tab.reLaunch('/pages/index');
			}
		}
		// getTokenForFile() {
		// 	/** 持久化存储数据读取 （token自动登录）*/
		// 	let loginToken = null;
		// 	this.$filePersistentIO.read('JTG_USR_TOKEN_INFO.txt').then((res) => {
		// 		loginToken = res;
		// 		return loginToken;
		// 	});
		// }
	}
};
</script>

<style lang="scss">
@import '@/static/scss/index.scss';
</style>